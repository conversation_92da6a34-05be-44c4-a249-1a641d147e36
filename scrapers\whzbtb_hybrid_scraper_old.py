# /scrapers/whzbtb_scraper.py (混合模式最终版)

import time
import random
import json
import os
import requests
import asyncio
from typing import List, Optional, Dict, AsyncGenerator

# --- 统一将所有依赖项放在文件顶部 ---
from selenium import webdriver
from selenium.webdriver.edge.options import Options as EdgeOptions
from selenium.webdriver.edge.service import Service as EdgeService
from selenium.webdriver.common.action_chains import ActionChains

from models.data_model import BiddingData
from scrapers.base_scraper import BaseScraper
from core.db_manager import DatabaseManager
from core.utils import standardize_price

class WhzbtbHybridScraper(BaseScraper):
    """
    武汉市公共资源交易平台爬虫 - 混合模式。
    - 优先使用requests，失败后自动降级到Selenium。
    - 采用增量批处理方式获取新数据。
    - 已适配框架的异步调用模式。
    """
    platform_name = "武汉市公共资源交易平台"
    platform_id = "whzbtb"
    base_url = "https://www.whzbtb.com"
    
    api_endpoints = {
        '中标': "https://www.whzbtb.com/V2PRTS/WinBidBulletinInfoList.do",
        '招标': "https://www.whzbtb.com/V2PRTS/TendererNoticeInfoList.do" # 假设的招标API
    }

    def __init__(self, headless=True, progress_callback=None):
        super().__init__()
        self.headless = headless
        self.progress_callback = progress_callback
        self.failure_counts = {'中标': 0, '招标': 0}
        self.max_failures = 2
        self.selenium_mode = {'中标': False, '招标': False}
        self.session = requests.Session()
        self.session.headers.update({
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36 Edg/120.0.0.0'
        })
        self.driver = None
        self.session_initialized = {'中标': False, '招标': False}

    def __del__(self):
        """析构函数，确保浏览器被关闭"""
        self._close_browser()

    def _setup_browser(self):
        """设置无头浏览器，优先Edge，失败后尝试Chrome"""
        if self.driver is not None:
            return

        # 通用浏览器选项
        common_args = [
            '--no-sandbox',
            '--disable-dev-shm-usage',
            '--disable-gpu',
            '--disable-web-security',
            '--allow-running-insecure-content',
            '--disable-extensions',
            '--disable-plugins',
            '--disable-images',
            '--user-agent=Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36 Edg/120.0.0.0'
        ]

        if self.headless:
            common_args.append('--headless')

        # 首先尝试Edge
        try:
            options = EdgeOptions()
            for arg in common_args:
                options.add_argument(arg)

            self.driver = webdriver.Edge(options=options)
            self._log_message("✅ Edge浏览器启动成功")
            return

        except Exception as e:
            self._log_message(f"⚠️ Edge浏览器启动失败: {e}")

        # Edge失败后尝试Chrome
        try:
            from selenium.webdriver.chrome.options import Options as ChromeOptions
            options = ChromeOptions()
            for arg in common_args:
                options.add_argument(arg)

            self.driver = webdriver.Chrome(options=options)
            self._log_message("✅ Chrome浏览器启动成功")
            return

        except Exception as e:
            self._log_message(f"⚠️ Chrome浏览器启动失败: {e}")

        # 都失败了
        raise Exception("无法启动任何浏览器驱动 (Edge/Chrome)")

    def _close_browser(self):
        """关闭浏览器"""
        if self.driver:
            try:
                self.driver.quit()
                self.driver = None
                self._log_message("✅ 浏览器已关闭")
            except:
                pass

    def _initialize_session_with_browser(self, announcement_type: str) -> bool:
        """使用无头浏览器自动获取会话信息"""
        try:
            # 如果已经初始化过，直接返回
            if self.session_initialized.get(announcement_type, False):
                return True

            self._log_message(f"🌐 使用浏览器获取{announcement_type}会话信息...")

            # 设置浏览器
            self._setup_browser()

            # 根据公告类型访问对应的首页
            if announcement_type == '中标':
                home_url = "https://www.whzbtb.com/V2PRTS/WinBidBulletinInfoListInit.do"
            else:
                home_url = "https://www.whzbtb.com/V2PRTS/TendererNoticeInfoListInit.do"

            # 访问首页
            self.driver.get(home_url)
            time.sleep(3)  # 等待页面加载

            # 获取所有Cookie
            cookies = self.driver.get_cookies()

            # 将Cookie添加到requests session
            for cookie in cookies:
                self.session.cookies.set(cookie['name'], cookie['value'])

            # 更新请求头以匹配浏览器行为
            self.session.headers.update({
                'Accept': 'application/json, text/javascript, */*; q=0.01',
                'Accept-Language': 'zh-CN,zh;q=0.9',
                'Content-Type': 'application/x-www-form-urlencoded; charset=UTF-8',
                'Origin': 'https://www.whzbtb.com',
                'Referer': home_url,
                'Sec-Fetch-Dest': 'empty',
                'Sec-Fetch-Mode': 'cors',
                'Sec-Fetch-Site': 'same-origin',
                'X-Requested-With': 'XMLHttpRequest',
                'sec-ch-ua': '"Not)A;Brand";v="8", "Chromium";v="138", "Microsoft Edge";v="138"',
                'sec-ch-ua-mobile': '?0',
                'sec-ch-ua-platform': '"Windows"'
            })

            # 标记为已初始化
            self.session_initialized[announcement_type] = True

            self._log_message(f"✅ {announcement_type}浏览器会话获取成功，Cookie数量: {len(cookies)}")

            # 关闭浏览器
            self._close_browser()

            return True

        except Exception as e:
            self._log_message(f"❌ {announcement_type}浏览器会话获取失败: {e}")
            self._close_browser()
            return False

    def _initialize_session(self, announcement_type: str) -> bool:
        """初始化会话，优先使用浏览器自动获取"""
        # 首先尝试浏览器自动获取
        if self._initialize_session_with_browser(announcement_type):
            return True

        # 浏览器失败后，回退到简单的requests方式
        try:
            # 根据公告类型访问对应的初始化页面
            if announcement_type == '中标':
                init_url = "https://www.whzbtb.com/V2PRTS/WinBidBulletinInfoListInit.do"
            else:
                init_url = "https://www.whzbtb.com/V2PRTS/TendererNoticeInfoListInit.do"

            # 访问初始化页面获取会话
            response = self.session.get(init_url, timeout=30)
            response.raise_for_status()

            # 更新请求头以匹配浏览器行为
            self.session.headers.update({
                'Accept': 'application/json, text/javascript, */*; q=0.01',
                'Accept-Language': 'zh-CN,zh;q=0.9',
                'Content-Type': 'application/x-www-form-urlencoded; charset=UTF-8',
                'Origin': 'https://www.whzbtb.com',
                'Referer': init_url,
                'Sec-Fetch-Dest': 'empty',
                'Sec-Fetch-Mode': 'cors',
                'Sec-Fetch-Site': 'same-origin',
                'X-Requested-With': 'XMLHttpRequest',
                'sec-ch-ua': '"Not)A;Brand";v="8", "Chromium";v="138", "Microsoft Edge";v="138"',
                'sec-ch-ua-mobile': '?0',
                'sec-ch-ua-platform': '"Windows"'
            })

            self._log_message(f"✅ {announcement_type}简单会话初始化成功")
            return True

        except Exception as e:
            self._log_message(f"❌ {announcement_type}会话初始化完全失败: {e}")
            return False

    def _fetch_data_with_requests(self, page_number: int, rows_per_page: int, announcement_type: str) -> Optional[dict]:
        """【同步】使用纯requests模式获取数据"""
        try:
            api_url = self.api_endpoints.get(announcement_type)
            if not api_url: return None

            # 首次请求时初始化会话
            if page_number == 1:
                if not self._initialize_session(announcement_type):
                    return None

            payload = {'page': page_number, 'rows': rows_per_page}
            response = self.session.post(api_url, data=payload, timeout=30)
            
            # 智能降级：如果遇到认证错误，立即返回失败，加速切换到Selenium
            if response.status_code in [401, 403]:
                print(f"[{self.platform_name}] Requests请求被拒绝 (状态码: {response.status_code})，可能需要Selenium。")
                return None
            
            response.raise_for_status()
            data = response.json()
            
            if data and 'rows' in data:
                return data
            return None
        except requests.RequestException as e:
            print(f"[{self.platform_name}] Requests模式获取失败: {e}")
            return None

    def _get_cookies_with_selenium(self) -> bool:
        """【同步】使用Selenium获取有效的cookies"""
        print(f"[{self.platform_name}] 正在启动Selenium以获取有效会话...")
        edge_options = EdgeOptions()
        edge_options.add_argument('--no-sandbox')
        edge_options.add_argument('--disable-dev-shm-usage')
        edge_options.add_argument('--disable-blink-features=AutomationControlled')
        edge_options.add_experimental_option("excludeSwitches", ["enable-automation"])
        if self.headless:
            edge_options.add_argument('--headless=new')
        
        edge_driver_path = os.path.join(os.path.dirname(os.path.dirname(os.path.abspath(__file__))), 'core', 'msedgedriver.exe')
        edge_service = EdgeService(executable_path=edge_driver_path)
        
        driver = None
        try:
            driver = webdriver.Edge(service=edge_service, options=edge_options)
            driver.execute_script("Object.defineProperty(navigator, 'webdriver', {get: () => undefined})")
            driver.set_page_load_timeout(120)

            driver.get(self.base_url)
            time.sleep(random.uniform(3, 5))
            
            # 将Selenium的cookies更新到requests的session中
            cookies = driver.get_cookies()
            for cookie in cookies:
                self.session.cookies.set(cookie['name'], cookie['value'])
            
            print(f"[{self.platform_name}] Selenium成功获取并设置了 {len(cookies)} 个Cookie。")
            return True
        except Exception as e:
            print(f"[{self.platform_name}] Selenium获取会话失败: {e}")
            return False
        finally:
            if driver:
                driver.quit()

    async def _fetch_data_batch(self, page_number: int, rows_per_page: int, announcement_type: str) -> Optional[dict]:
        """【异步】智能降级数据获取：优先requests，失败后自动切换到Selenium"""
        if self.selenium_mode.get(announcement_type, False):
            # 如果已处于Selenium模式，直接使用
            return await asyncio.to_thread(self._fetch_data_with_requests, page_number, rows_per_page, announcement_type)

        # 优先尝试纯requests模式
        data = await asyncio.to_thread(self._fetch_data_with_requests, page_number, rows_per_page, announcement_type)
        if data is not None:
            self.failure_counts[announcement_type] = 0 # 成功后重置失败计数
            return data

        # requests模式失败，增加失败计数
        self.failure_counts[announcement_type] += 1
        if self.failure_counts[announcement_type] >= self.max_failures:
            print(f"[{self.platform_name}] {announcement_type}类型达到最大失败次数，切换到Selenium模式。")
            self.selenium_mode[announcement_type] = True
            # 切换后，立即尝试用Selenium获取一次cookie
            success = await asyncio.to_thread(self._get_cookies_with_selenium)
            if success:
                # 再次尝试用带有新cookie的requests请求
                return await asyncio.to_thread(self._fetch_data_with_requests, page_number, rows_per_page, announcement_type)
        return None

    def _log_message(self, message: str):
        """统一的日志输出方法"""
        print(message)
        if self.progress_callback:
            try:
                self.progress_callback(message, 0)  # 武汉爬虫的回调格式：(message, progress)
            except:
                pass  # 忽略回调错误

    def _report_progress(self, message: str, progress: float):
        """报告进度"""
        self._log_message(message)
        if self.progress_callback:
            try:
                # 确保进度信息格式正确，便于GUI解析
                formatted_message = message
                if "第" not in message and "页" not in message and progress < 100:
                    # 如果消息中没有页数信息，尝试添加
                    if "批次" in message or "/" in message:
                        formatted_message = message  # 保持原格式
                    else:
                        # 添加通用页数格式
                        current_batch = int(progress / 100 * 10) + 1 if progress > 0 else 1
                        formatted_message = f"第{current_batch}/10页 - {message}"

                self.progress_callback(formatted_message, progress)
            except:
                pass  # 忽略回调错误

    async def scrape_all_types(self, db: DatabaseManager) -> List[BiddingData]:
        """
        爬取所有类型的数据（中标+招标），按子类进度更新
        """
        all_items = []
        announcement_types = ['中标', '招标']
        total_types = len(announcement_types)

        for i, announcement_type in enumerate(announcement_types):
            self._log_message(f"开始爬取{announcement_type}数据...")

            # 爬取当前类型的数据
            type_items = []
            async for item in self.scrape_list(db, announcement_type):
                type_items.append(item)

            all_items.extend(type_items)

            # 按子类进度更新：每完成一个类型，进度增加 1/total_types
            progress = ((i + 1) / total_types) * 100
            self._report_progress(f"{announcement_type}数据爬取完成 ({len(type_items)}条)", progress)

        return all_items

    def set_progress_callback(self, callback):
        """设置进度回调函数"""
        self.progress_callback = callback

    async def scrape_list(self, db: DatabaseManager, announcement_type: str = '中标') -> AsyncGenerator[BiddingData, None]:
        """
        【异步】爬取单一类型的数据 - 一次性获取，分批解析版本。
        这是暴露给外部调用的主要接口。
        """
        self._log_message(f"开始混合爬取{announcement_type}数据...")

        # 第一步：获取总数据量
        self._report_progress("正在获取数据总量...", 5.0)
        basic_data = await self._fetch_data_batch(1, 10, announcement_type)
        if not basic_data or 'total' not in basic_data:
            self._log_message(f"无法获取{announcement_type}基本信息，任务中止。")
            return

        total_count = int(basic_data['total'])
        self._log_message(f"服务器{announcement_type}总数据量: {total_count}")

        # 第二步：检查数据库已有数量
        existing_count = await asyncio.to_thread(db.count_items, self.platform_id, announcement_type)
        new_data_count = total_count - existing_count

        if new_data_count <= 0:
            self._report_progress("数据库数据已是最新，无需爬取。", 100.0)
            return

        self._log_message(f"数据库已有: {existing_count}, 计划获取新数据: {new_data_count}")

        # 第三步：一次性获取所有新数据
        self._report_progress(f"正在获取 {new_data_count} 条新数据...", 10.0)
        all_data = await self._fetch_data_batch(1, new_data_count, announcement_type)

        if not all_data or 'rows' not in all_data:
            self._log_message("获取数据失败，任务中止。")
            return

        all_rows = all_data['rows']
        actual_count = len(all_rows)
        self._log_message(f"实际获取到 {actual_count} 条数据")

        # 第四步：分批解析和存储
        batch_size = 1000  # 每批解析1000条
        processed = 0
        total_batches = (actual_count + batch_size - 1) // batch_size

        for batch_num in range(1, total_batches + 1):
            start_idx = (batch_num - 1) * batch_size
            end_idx = min(start_idx + batch_size, actual_count)
            batch_rows = all_rows[start_idx:end_idx]

            # 报告解析进度
            progress_percent = 10 + (batch_num / total_batches) * 85  # 10%-95%用于解析
            self._report_progress(f"解析第{batch_num}/{total_batches}批 ({len(batch_rows)}条)", progress_percent)

            # 解析当前批次的数据
            for item_json in batch_rows:
                    # 解析JSON数据
                    try:
                        # 根据公告类型使用不同的字段映射
                        if announcement_type == '中标':
                            # 中标数据：优先使用prjName字段
                            title = item_json.get('prjName', '').strip()
                            if not title:
                                title = item_json.get('tenderPrjName', '').strip()
                        else:
                            # 招标数据：优先使用tenderPrjName字段
                            title = item_json.get('tenderPrjName', '').strip()
                            if not title:
                                title = item_json.get('prjName', '').strip()

                        if not title:
                            title = "未知项目"

                        # 构建详情页URL - 使用id字段
                        detail_id = item_json.get('id', '')
                        if detail_id:
                            if announcement_type == '中标':
                                url = f"{self.base_url}/V2PRTS/WinBidBulletinInfoDetail.do?id={detail_id}"
                            else:
                                url = f"{self.base_url}/V2PRTS/TendererNoticeInfoDetail.do?id={detail_id}"
                        else:
                            # 备选：使用API端点作为URL
                            url = self.api_endpoints.get(announcement_type, '')

                        # 根据公告类型解析发布日期
                        if announcement_type == '中标':
                            # 中标数据：使用insertDate字段
                            publish_date = item_json.get('insertDate', '')
                            if not publish_date:
                                publish_date = item_json.get('noticeStartDate', '')
                        else:
                            # 招标数据：使用noticeStartDate字段
                            publish_date = item_json.get('noticeStartDate', '')

                        if publish_date and isinstance(publish_date, str):
                            # 截取日期部分 "2024-07-11 19:00" -> "2024-07-11"
                            if len(publish_date) >= 10:
                                publish_date = publish_date[:10]
                        else:
                            publish_date = "2025-01-01"

                        # 地区固定为武汉
                        region = '武汉'

                        # 根据公告类型提取价格信息和编号
                        price_text = ""
                        price_normalized = None
                        tender_reference = ""

                        if announcement_type == '中标':
                            # 中标数据：使用winBidPrice字段
                            win_bid_price = item_json.get('winBidPrice')
                            if win_bid_price and isinstance(win_bid_price, (int, float)) and win_bid_price > 0:
                                price_text = f"{win_bid_price}万元"
                                price_normalized = float(win_bid_price)

                            # 中标编号：使用publicityNumber字段
                            tender_reference = item_json.get('publicityNumber', '')
                            if not tender_reference:
                                tender_reference = item_json.get('tenderReference', '')
                        else:
                            # 招标数据：使用totalInvestment字段
                            total_investment = item_json.get('totalInvestment')
                            if total_investment and isinstance(total_investment, (int, float)) and total_investment > 0:
                                price_text = f"{total_investment}万元"
                                price_normalized = float(total_investment)

                            # 招标编号：使用registrationId字段
                            tender_reference = item_json.get('registrationId', '')

                        # 创建BiddingData对象
                        item = BiddingData(
                            platform_id=self.platform_id,
                            title=title,
                            url=url,
                            publish_date=publish_date,
                            region=region,
                            announcement_type=announcement_type,
                            price_text=price_text,
                            price_normalized=price_normalized,
                            price_type="WINNING" if announcement_type == '中标' and price_text else ("INVESTMENT" if price_text else "UNKNOWN"),
                            tender_reference=tender_reference
                        )
                        yield item # 逐条产出

                    except Exception as e:
                        self._log_message(f"解析数据项失败: {e}")
                        continue

                processed += len(rows)

                # 报告批次完成进度
                progress_percent = (processed / new_data_count) * 100
                self._report_progress(f"第{batch_num}/{total_batches}页完成 (已处理{processed}/{new_data_count}条)", progress_percent)

                await asyncio.sleep(random.uniform(3, 8)) # 批次间友好等待
            else:
                self._log_message("批次获取失败，停止处理。")
                break

        # 完成进度报告
        if processed > 0:
            self._report_progress(f"爬取完成，共获取 {processed} 条新数据", 100.0)

    # --- 为兼容框架而保留/修改的方法 ---
    async def get_total_items(self, announcement_type: str = '中标') -> int:
        """获取总数据量"""
        basic_data = await self._fetch_data_batch(1, 1, announcement_type)
        return int(basic_data['total']) if basic_data and 'total' in basic_data else 0

    async def get_max_pages(self, announcement_type: str = '中标') -> int:
        """在批处理模式下，页数意义不大，但为保持接口完整性而计算。"""
        total_items = await self.get_total_items(announcement_type)
        return (total_items + 999) // 1000 # 假设每批1000条

    async def extract_details(self, item: BiddingData) -> BiddingData:
        """所有信息已在列表阶段获取，无需操作。"""
        return item
