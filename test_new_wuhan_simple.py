#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
简化测试新版武汉爬虫 - 只获取少量数据
"""

import sys
import os
import asyncio
import time

# 添加项目根目录到路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from scrapers.whzbtb_hybrid_scraper_new import WhzbtbHybridScraperNew
from core.db_manager import DatabaseManager

class SimpleProgressCallback:
    """简单进度回调"""
    
    def __init__(self):
        self.start_time = time.time()
    
    def __call__(self, message, progress):
        """进度回调函数"""
        elapsed = time.time() - self.start_time
        print(f"[{elapsed:6.1f}s] {progress:5.1f}% - {message}")

async def test_simple():
    """简化测试"""
    print("🚀 简化测试新版武汉爬虫")
    print("=" * 60)
    
    # 创建进度回调
    progress_callback = SimpleProgressCallback()
    
    # 创建新版爬虫实例
    scraper = WhzbtbHybridScraperNew(headless=True, progress_callback=progress_callback)
    
    try:
        print("1️⃣ 测试基本API连接...")
        print("-" * 40)
        
        # 测试获取总数
        total_tender = await scraper.get_total_items('招标')
        total_winbid = await scraper.get_total_items('中标')
        
        print(f"招标总数: {total_tender}")
        print(f"中标总数: {total_winbid}")
        
        print("\n2️⃣ 测试数据解析...")
        print("-" * 40)
        
        # 测试获取少量数据
        test_data = await scraper._fetch_data_batch(1, 5, '招标')
        
        if test_data and 'rows' in test_data:
            print(f"✅ 成功获取 {len(test_data['rows'])} 条测试数据")
            
            # 测试解析
            for i, item_json in enumerate(test_data['rows'][:3]):
                parsed_item = scraper._parse_item(item_json, '招标')
                if parsed_item:
                    print(f"  样本{i+1}: {parsed_item.title[:30]}... | {parsed_item.price_text}")
                else:
                    print(f"  样本{i+1}: 解析失败")
        else:
            print("❌ 获取测试数据失败")
            
        print("\n3️⃣ 测试完整流程（少量数据）...")
        print("-" * 40)
        
        # 创建数据库管理器
        db = DatabaseManager()
        
        # 临时修改获取数量，只获取10条数据测试
        original_method = scraper._fetch_data_batch
        
        async def limited_fetch(page, rows, announcement_type):
            # 限制最多获取10条数据
            limited_rows = min(rows, 10)
            return await original_method(page, limited_rows, announcement_type)
        
        scraper._fetch_data_batch = limited_fetch
        
        # 测试招标数据
        tender_items = []
        count = 0
        
        start_time = time.time()
        async for item in scraper.scrape_list_async(db, '招标'):
            tender_items.append(item)
            count += 1
            if count >= 10:  # 最多10条
                break
        
        elapsed_time = time.time() - start_time
        
        print(f"\n📋 测试结果:")
        print(f"  获取数据: {len(tender_items)} 条")
        print(f"  耗时: {elapsed_time:.1f} 秒")
        
        if tender_items:
            sample = tender_items[0]
            print(f"  样本标题: {sample.title[:50]}...")
            print(f"  样本价格: {sample.price_text}")
            print(f"  样本日期: {sample.publish_date}")
            print(f"  样本URL: {sample.url}")
            
            success = True
        else:
            success = False
        
        db.close()
        
        print(f"\n" + "=" * 60)
        if success:
            print("✅ 简化测试成功！新版武汉爬虫基本功能正常。")
            print("\n💡 测试结论:")
            print("  - API连接正常")
            print("  - 数据解析正常")
            print("  - 进度回调正常")
            print("  - 异步生成器正常")
        else:
            print("❌ 简化测试失败，需要进一步调试。")
        
        return success
        
    except Exception as e:
        print(f"❌ 测试过程中出现错误: {e}")
        import traceback
        traceback.print_exc()
        return False
    
    finally:
        # 确保浏览器被关闭
        scraper._close_browser()

async def main():
    """主测试函数"""
    success = await test_simple()
    
    if success:
        print(f"\n🎉 新版武汉爬虫可以投入使用！")
        print(f"\n📝 下一步:")
        print(f"  1. 将新版本替换原有版本")
        print(f"  2. 在GUI中测试完整功能")
        print(f"  3. 验证大数据量处理能力")
    else:
        print(f"\n⚠️ 需要进一步调试和优化。")

if __name__ == "__main__":
    asyncio.run(main())
