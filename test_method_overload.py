#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
测试武汉爬虫的方法重载是否正确工作
"""

import sys
import os
import asyncio
import inspect

# 添加项目根目录到路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from scrapers.whzbtb_hybrid_scraper import WhzbtbHybridScraperNew
from core.db_manager import DatabaseManager

def test_method_overload():
    """测试方法重载"""
    print("🔧 测试武汉爬虫方法重载")
    print("=" * 60)
    
    # 创建爬虫实例
    scraper = WhzbtbHybridScraperNew(headless=True)
    
    print("1️⃣ 检查方法存在性...")
    print("-" * 40)
    
    # 检查方法是否存在
    has_scrape_list = hasattr(scraper, 'scrape_list')
    has_scrape_list_async = hasattr(scraper, 'scrape_list_async')
    has_wrapper = hasattr(scraper, '_scrape_list_async_wrapper')
    
    print(f"scrape_list 方法存在: {'✅' if has_scrape_list else '❌'}")
    print(f"scrape_list_async 方法存在: {'✅' if has_scrape_list_async else '❌'}")
    print(f"_scrape_list_async_wrapper 方法存在: {'✅' if has_wrapper else '❌'}")
    
    if not has_scrape_list:
        print("❌ 核心方法不存在，测试失败")
        return False
    
    print("\n2️⃣ 测试方法签名...")
    print("-" * 40)
    
    # 获取方法签名
    scrape_list_method = getattr(scraper, 'scrape_list')
    signature = inspect.signature(scrape_list_method)
    
    print(f"scrape_list 方法签名: {signature}")
    print(f"方法类型: {type(scrape_list_method)}")
    
    print("\n3️⃣ 测试基类调用（同步）...")
    print("-" * 40)
    
    try:
        # 测试基类调用（同步版本）
        result = scraper.scrape_list(10)  # 传入int参数
        print(f"基类调用结果: {type(result)} (长度: {len(result)})")
        print("✅ 基类同步调用成功")
        base_sync_ok = True
    except Exception as e:
        print(f"❌ 基类同步调用失败: {e}")
        base_sync_ok = False
    
    print("\n4️⃣ 测试GUI调用（异步）...")
    print("-" * 40)
    
    try:
        # 创建数据库管理器
        db = DatabaseManager()
        
        # 测试GUI调用（异步版本）
        result = scraper.scrape_list(db, '招标')  # 传入DatabaseManager参数
        print(f"GUI调用结果: {type(result)}")
        
        # 检查是否是异步生成器
        is_async_generator = hasattr(result, '__aiter__')
        print(f"是否为异步生成器: {'✅' if is_async_generator else '❌'}")
        
        if is_async_generator:
            print("✅ GUI异步调用成功")
            gui_async_ok = True
        else:
            print("❌ GUI异步调用返回类型错误")
            gui_async_ok = False
        
        db.close()
        
    except Exception as e:
        print(f"❌ GUI异步调用失败: {e}")
        import traceback
        traceback.print_exc()
        gui_async_ok = False
    
    print(f"\n" + "=" * 60)
    print("📋 测试总结:")
    print("=" * 60)
    
    print(f"基类同步调用: {'✅ 成功' if base_sync_ok else '❌ 失败'}")
    print(f"GUI异步调用: {'✅ 成功' if gui_async_ok else '❌ 失败'}")
    
    overall_success = base_sync_ok and gui_async_ok
    
    if overall_success:
        print(f"\n🎉 方法重载测试成功！")
        print(f"\n💡 验证结果:")
        print(f"  - 基类同步接口正常工作")
        print(f"  - GUI异步接口正常工作")
        print(f"  - 方法重载机制正确")
        print(f"\n🚀 武汉爬虫现在可以在GUI中正常使用！")
    else:
        print(f"\n⚠️ 方法重载仍有问题，需要进一步调试。")
    
    return overall_success

def main():
    """主测试函数"""
    success = test_method_overload()
    
    if success:
        print(f"\n✅ 方法重载修复完成！")
    else:
        print(f"\n❌ 仍需进一步调试。")

if __name__ == "__main__":
    main()
