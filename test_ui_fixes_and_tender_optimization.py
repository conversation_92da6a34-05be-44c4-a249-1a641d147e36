#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
测试UI修复和招标公告优化的脚本
"""

import sys
import os
import time
import asyncio
from bs4 import BeautifulSoup

# 添加项目根目录到路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from utils.intelligent_extractor import IntelligentExtractor

def test_announcement_type_optimization():
    """测试公告类型优化"""
    print("🧪 测试公告类型优化")
    print("=" * 60)
    
    # 创建提取器
    extractor = IntelligentExtractor("cnzhaobiao")
    
    # 测试招标公告字段优化
    print("📋 招标公告字段优化测试:")
    tender_fields = extractor._get_optimized_fields("招标")
    
    print(f"  💰 价格关键词 ({len(tender_fields['price'])} 个):")
    print(f"     前5个: {tender_fields['price'][:5]}")
    print(f"     招标特有: {[kw for kw in tender_fields['price'][:10] if '预算' in kw or '限价' in kw or '控制价' in kw]}")
    
    print(f"  🔢 招标编号关键词 ({len(tender_fields['tender_reference'])} 个):")
    print(f"     前5个: {tender_fields['tender_reference'][:5]}")
    
    if 'tender_deadline' in tender_fields:
        print(f"  ⏰ 招标截止时间关键词 ({len(tender_fields['tender_deadline'])} 个):")
        print(f"     示例: {tender_fields['tender_deadline'][:3]}")
    
    # 测试中标公告字段优化
    print(f"\n📋 中标公告字段优化测试:")
    bid_fields = extractor._get_optimized_fields("中标")
    
    print(f"  💰 价格关键词 ({len(bid_fields['price'])} 个):")
    print(f"     前5个: {bid_fields['price'][:5]}")
    print(f"     中标特有: {[kw for kw in bid_fields['price'][:10] if '中标' in kw or '成交' in kw]}")
    
    if 'winner_info' in bid_fields:
        print(f"  🏆 中标单位关键词 ({len(bid_fields['winner_info'])} 个):")
        print(f"     示例: {bid_fields['winner_info'][:3]}")

def test_extraction_with_mock_html():
    """使用模拟HTML测试提取效果"""
    print("\n🧪 模拟HTML提取测试")
    print("=" * 60)
    
    # 模拟招标公告HTML
    tender_html = """
    <html>
    <body>
        <div class="content">
            <h1>某市政道路建设项目招标公告</h1>
            <table>
                <tr><td>项目编号</td><td>ZB2025-001</td></tr>
                <tr><td>预算金额</td><td>1500万元</td></tr>
                <tr><td>最高限价</td><td>1480万元</td></tr>
                <tr><td>投标截止时间</td><td>2025-02-15 09:00</td></tr>
                <tr><td>招标单位</td><td>某市建设局</td></tr>
            </table>
        </div>
    </body>
    </html>
    """
    
    # 模拟中标公告HTML
    bid_html = """
    <html>
    <body>
        <div class="content">
            <h1>某市政道路建设项目中标公告</h1>
            <table>
                <tr><td>项目编号</td><td>ZB2025-001</td></tr>
                <tr><td>中标价</td><td>1420万元</td></tr>
                <tr><td>中标单位</td><td>某建筑工程有限公司</td></tr>
                <tr><td>公示编号</td><td>GS2025-001</td></tr>
            </table>
        </div>
    </body>
    </html>
    """
    
    extractor = IntelligentExtractor("cnzhaobiao")
    
    # 测试招标公告提取
    print("📝 招标公告提取测试:")
    tender_soup = BeautifulSoup(tender_html, 'html.parser')
    tender_result = extractor.extract_information(tender_soup, "招标")
    
    for field, value in tender_result.items():
        if value:
            print(f"  ✅ {field}: {value}")
    
    # 测试中标公告提取
    print(f"\n📝 中标公告提取测试:")
    bid_soup = BeautifulSoup(bid_html, 'html.parser')
    bid_result = extractor.extract_information(bid_soup, "中标")
    
    for field, value in bid_result.items():
        if value:
            print(f"  ✅ {field}: {value}")
    
    # 对比分析
    print(f"\n📊 提取结果对比:")
    print(f"  招标公告提取字段数: {len([v for v in tender_result.values() if v])}")
    print(f"  中标公告提取字段数: {len([v for v in bid_result.values() if v])}")

def simulate_ui_progress_updates():
    """模拟UI进度更新测试"""
    print("\n🎨 UI进度更新模拟测试")
    print("=" * 60)
    
    print("📋 模拟进度更新场景:")
    
    # 模拟不同的进度状态
    test_scenarios = [
        {"current": 0, "total": 100, "status": "准备中", "expected_color": "#ffc107"},
        {"current": 25, "total": 100, "status": "运行中", "expected_color": "#007bff"},
        {"current": 75, "total": 100, "status": "运行中", "expected_color": "#007bff"},
        {"current": 100, "total": 100, "status": "完成", "expected_color": "#28a745"},
    ]
    
    for i, scenario in enumerate(test_scenarios, 1):
        current = scenario["current"]
        total = scenario["total"]
        progress = current / total if total > 0 else 0
        progress_percent = progress * 100
        
        print(f"\n  {i}. {scenario['status']}状态:")
        print(f"     进度: {current}/{total} ({progress_percent:.1f}%)")
        print(f"     预期颜色: {scenario['expected_color']}")
        
        # 模拟状态文本生成
        if progress >= 1.0:
            text = f"✅ 第{current}页 (100%)"
            color = "#28a745"
        elif progress > 0:
            text = f"▶️ 第{current}页 ({progress_percent:.1f}%)"
            color = "#007bff"
        else:
            text = "🔄 准备中..."
            color = "#ffc107"
        
        print(f"     实际文本: {text}")
        print(f"     实际颜色: {color}")
        print(f"     颜色匹配: {'✅' if color == scenario['expected_color'] else '❌'}")

def test_platform_status_updates():
    """测试平台状态更新"""
    print("\n🔧 平台状态更新测试")
    print("=" * 60)
    
    # 状态颜色映射
    status_colors = {
        'waiting': "#6c757d",    # 灰色 - 待机
        'preparing': "#ffc107",  # 黄色 - 准备中
        'running': "#007bff",    # 蓝色 - 运行中
        'completed': "#28a745",  # 绿色 - 完成
        'error': "#dc3545",      # 红色 - 错误
        'stopped': "#fd7e14"     # 橙色 - 已停止
    }
    
    status_icons = {
        'waiting': "⏸️",
        'preparing': "🔄",
        'running': "▶️",
        'completed': "✅",
        'error': "❌",
        'stopped': "⏹️"
    }
    
    print("📋 状态映射测试:")
    for status, color in status_colors.items():
        icon = status_icons.get(status, "")
        print(f"  {status:>10}: {icon} 颜色 {color}")

def main():
    """主测试函数"""
    print("🚀 UI修复和招标公告优化测试")
    print("=" * 80)
    
    try:
        # 1. 测试公告类型优化
        test_announcement_type_optimization()
        
        # 2. 测试模拟HTML提取
        test_extraction_with_mock_html()
        
        # 3. 模拟UI进度更新
        simulate_ui_progress_updates()
        
        # 4. 测试平台状态更新
        test_platform_status_updates()
        
        # 总结
        print("\n" + "=" * 80)
        print("📋 测试总结:")
        print("=" * 80)
        
        print("✅ 招标公告优化:")
        print("  - 重点关注预算金额、最高限价等招标特有信息")
        print("  - 增强项目编号和招标编号的识别能力")
        print("  - 添加投标截止时间等招标特有字段")
        
        print("\n✅ 中标公告优化:")
        print("  - 重点关注中标价、成交价等成交信息")
        print("  - 增强中标单位、中标供应商的识别")
        print("  - 保持原有的详细价格提取能力")
        
        print("\n✅ UI显示修复:")
        print("  - 进度条文字和颜色正确同步")
        print("  - 状态指示器颜色与进度状态匹配")
        print("  - 添加了状态图标和详细进度信息")
        
        print("\n💡 优化效果:")
        print("  - 招标公告：简化提取，重点关注关联信息")
        print("  - 中标公告：保持详细提取，支持完整价格分析")
        print("  - UI体验：实时状态反馈，颜色同步显示")
        
    except Exception as e:
        print(f"❌ 测试过程中发生错误: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
