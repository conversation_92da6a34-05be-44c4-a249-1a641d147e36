#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
测试修复后的武汉爬虫GUI接口
"""

import sys
import os
import asyncio
import time

# 添加项目根目录到路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from scrapers.whzbtb_hybrid_scraper import WhzbtbHybridScraperNew
from core.db_manager import DatabaseManager

class TestProgressCallback:
    """测试进度回调"""
    
    def __init__(self):
        self.start_time = time.time()
    
    def __call__(self, message, progress):
        """进度回调函数"""
        elapsed = time.time() - self.start_time
        print(f"[{elapsed:6.1f}s] {progress:5.1f}% - {message}")

async def test_gui_interface():
    """测试GUI接口兼容性"""
    print("🔧 测试修复后的武汉爬虫GUI接口")
    print("=" * 60)
    
    # 创建进度回调
    progress_callback = TestProgressCallback()
    
    # 创建爬虫实例
    scraper = WhzbtbHybridScraperNew(headless=True, progress_callback=progress_callback)
    
    try:
        # 创建数据库管理器
        db = DatabaseManager()
        
        print("1️⃣ 测试GUI期望的异步接口...")
        print("-" * 40)
        
        # 测试GUI调用的方法签名
        print("检查方法签名:")
        
        # 检查 scrape_list 方法（GUI期望的接口）
        if hasattr(scraper, 'scrape_list'):
            print("✅ scrape_list 方法存在")
            
            # 测试调用（限制获取数量）
            items_collected = []
            count = 0
            max_items = 5  # 只获取5条测试
            
            print(f"开始测试异步生成器接口（最多{max_items}条）...")
            
            start_time = time.time()
            async for item in scraper.scrape_list(db, '招标'):
                items_collected.append(item)
                count += 1
                print(f"  获取第{count}条: {item.title[:30]}...")
                
                if count >= max_items:
                    break
            
            elapsed_time = time.time() - start_time
            
            print(f"\n📊 测试结果:")
            print(f"  获取数据: {len(items_collected)} 条")
            print(f"  耗时: {elapsed_time:.1f} 秒")
            print(f"  接口类型: 异步生成器 ✅")
            
            if items_collected:
                sample = items_collected[0]
                print(f"\n📋 样本数据:")
                print(f"  标题: {sample.title[:50]}...")
                print(f"  价格: {sample.price_text}")
                print(f"  日期: {sample.publish_date}")
                print(f"  URL: {sample.url}")
                
                success = True
            else:
                print("❌ 未获取到数据")
                success = False
        else:
            print("❌ scrape_list 方法不存在")
            success = False
        
        print("\n2️⃣ 测试基类兼容性...")
        print("-" * 40)
        
        # 测试基类方法
        base_result = scraper.scrape_list(10)  # 基类同步方法
        print(f"基类同步方法返回: {type(base_result)} (长度: {len(base_result)})")
        print("✅ 基类兼容性正常")
        
        db.close()
        
        print(f"\n" + "=" * 60)
        if success:
            print("✅ GUI接口修复成功！")
            print("\n💡 验证结果:")
            print("  - GUI异步接口正常工作")
            print("  - 异步生成器返回正确")
            print("  - 数据解析功能正常")
            print("  - 基类兼容性保持")
            print("\n🚀 武汉爬虫现在可以在GUI中正常使用！")
        else:
            print("❌ GUI接口仍有问题，需要进一步调试。")
        
        return success
        
    except Exception as e:
        print(f"❌ 测试过程中出现错误: {e}")
        import traceback
        traceback.print_exc()
        return False
    
    finally:
        # 确保浏览器被关闭
        scraper._close_browser()

async def main():
    """主测试函数"""
    success = await test_gui_interface()
    
    if success:
        print(f"\n🎉 修复完成！武汉爬虫现在可以在GUI中正常运行。")
    else:
        print(f"\n⚠️ 仍需进一步调试。")

if __name__ == "__main__":
    asyncio.run(main())
