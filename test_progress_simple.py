#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
简单测试武汉爬虫进度显示
"""

import sys
import os
import asyncio

# 添加项目根目录到路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from scrapers.whzbtb_hybrid_scraper import WhzbtbHybridScraper
from core.db_manager import DatabaseManager

def test_progress_callback(message, progress):
    """测试进度回调"""
    print(f"📊 进度: {progress:.1f}% - {message}")
    
    # 检查是否包含页数信息
    if "第" in message and "/" in message and "页" in message:
        try:
            page_info = message.split("第")[1].split("页")[0]
            current_page = int(page_info.split("/")[0])
            total_pages = int(page_info.split("/")[1])
            print(f"  ✅ 解析页数: {current_page}/{total_pages}")
        except Exception as e:
            print(f"  ❌ 页数解析失败: {e}")

async def test_simple():
    """简单测试"""
    print("🔧 简单测试武汉爬虫进度显示")
    print("=" * 50)
    
    # 创建爬虫实例
    scraper = WhzbtbHybridScraper(headless=True, progress_callback=test_progress_callback)
    
    try:
        # 测试进度报告方法
        print("1️⃣ 测试进度报告方法...")
        scraper._report_progress("测试消息", 25.5)
        scraper._report_progress("第2/10页 (1000条)", 50.0)
        scraper._report_progress("第5/10页完成 (已处理5000/10000条)", 75.0)
        
        print("\n2️⃣ 测试实际爬取进度...")
        
        # 创建数据库管理器
        db = DatabaseManager()
        
        # 获取少量数据测试进度
        count = 0
        async for item in scraper.scrape_list(db, '招标'):
            count += 1
            if count >= 10:  # 获取10条数据
                break
        
        print(f"\n✅ 获取了 {count} 条数据")
        
        db.close()
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
    
    finally:
        scraper._close_browser()

if __name__ == "__main__":
    asyncio.run(test_simple())
