#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
增强的匹配对话框
提供更好的用户体验和详细的匹配信息
"""

import tkinter as tk
from tkinter import ttk, messagebox
import customtkinter
import webbrowser
from typing import List, Tuple
from models.data_model import BiddingData


class EnhancedMatchDialog:
    """增强的匹配对话框"""
    
    def __init__(self, parent, current_item: BiddingData, matched_items: List[Tuple], 
                 target_type: str, match_details: str):
        self.parent = parent
        self.current_item = current_item
        self.matched_items = matched_items  # [(BiddingData, similarity, match_type), ...]
        self.target_type = target_type
        self.match_details = match_details
        self.dialog = None
        
    def show(self):
        """显示对话框"""
        self.dialog = tk.Toplevel(self.parent)
        self.dialog.title(f"相关{self.target_type}匹配结果")
        self.dialog.geometry("900x600")
        self.dialog.resizable(True, True)
        
        # 设置对话框居中
        self.dialog.transient(self.parent)
        self.dialog.grab_set()
        
        self._create_widgets()
        self._populate_data()
        
        # 居中显示
        self.dialog.update_idletasks()
        x = (self.dialog.winfo_screenwidth() // 2) - (self.dialog.winfo_width() // 2)
        y = (self.dialog.winfo_screenheight() // 2) - (self.dialog.winfo_height() // 2)
        self.dialog.geometry(f"+{x}+{y}")
    
    def _create_widgets(self):
        """创建界面组件"""
        # 主框架
        main_frame = ttk.Frame(self.dialog, padding="10")
        main_frame.pack(fill="both", expand=True)
        
        # 标题信息框架
        info_frame = ttk.LabelFrame(main_frame, text="当前项目信息", padding="10")
        info_frame.pack(fill="x", pady=(0, 10))
        
        # 当前项目标题
        current_title = self.current_item.title
        if len(current_title) > 80:
            current_title = current_title[:80] + "..."
        
        ttk.Label(info_frame, text=f"标题: {current_title}", 
                 font=("Microsoft YaHei UI", 10, "bold")).pack(anchor="w")
        ttk.Label(info_frame, text=f"类型: {self.current_item.announcement_type}公告").pack(anchor="w")
        ttk.Label(info_frame, text=f"发布日期: {self.current_item.publish_date}").pack(anchor="w")
        ttk.Label(info_frame, text=f"平台: {self.current_item.platform_id}").pack(anchor="w")
        
        # 匹配详情
        ttk.Label(info_frame, text=f"匹配详情: {self.match_details}", 
                 foreground="blue").pack(anchor="w", pady=(5, 0))
        
        # 匹配结果框架
        result_frame = ttk.LabelFrame(main_frame, text=f"找到的相关{self.target_type}", padding="10")
        result_frame.pack(fill="both", expand=True, pady=(0, 10))
        
        # 创建表格
        columns = ("序号", "标题", "发布日期", "匹配类型", "相似度", "操作")
        self.tree = ttk.Treeview(result_frame, columns=columns, show="headings", height=12)
        
        # 设置列标题和宽度
        column_widths = {"序号": 50, "标题": 300, "发布日期": 100, "匹配类型": 120, "相似度": 80, "操作": 120}
        for col in columns:
            self.tree.heading(col, text=col)
            self.tree.column(col, width=column_widths.get(col, 100), minwidth=50)
        
        # 添加滚动条
        scrollbar_y = ttk.Scrollbar(result_frame, orient="vertical", command=self.tree.yview)
        scrollbar_x = ttk.Scrollbar(result_frame, orient="horizontal", command=self.tree.xview)
        self.tree.configure(yscrollcommand=scrollbar_y.set, xscrollcommand=scrollbar_x.set)
        
        # 布局表格和滚动条
        self.tree.grid(row=0, column=0, sticky="nsew")
        scrollbar_y.grid(row=0, column=1, sticky="ns")
        scrollbar_x.grid(row=1, column=0, sticky="ew")
        
        result_frame.grid_rowconfigure(0, weight=1)
        result_frame.grid_columnconfigure(0, weight=1)
        
        # 绑定双击事件
        self.tree.bind("<Double-1>", self._on_item_double_click)
        self.tree.bind("<Button-3>", self._on_right_click)  # 右键菜单
        
        # 按钮框架
        button_frame = ttk.Frame(main_frame)
        button_frame.pack(fill="x", pady=(0, 0))
        
        # 操作按钮
        ttk.Button(button_frame, text="打开选中项目", 
                  command=self._open_selected).pack(side="left", padx=(0, 10))
        ttk.Button(button_frame, text="查看编号关联", 
                  command=self._show_reference_association).pack(side="left", padx=(0, 10))
        ttk.Button(button_frame, text="关闭", 
                  command=self.dialog.destroy).pack(side="right")
        
        # 状态栏
        self.status_var = tk.StringVar()
        status_bar = ttk.Label(main_frame, textvariable=self.status_var, 
                              relief="sunken", anchor="w")
        status_bar.pack(fill="x", pady=(10, 0))
        
        self.status_var.set(f"找到 {len(self.matched_items)} 个相关{self.target_type}")
    
    def _populate_data(self):
        """填充数据到表格"""
        for i, (matched_item, similarity, match_type) in enumerate(self.matched_items, 1):
            # 处理标题显示
            title = matched_item.title
            if len(title) > 40:
                title = title[:40] + "..."
            
            # 处理匹配类型显示
            if match_type.startswith('reference_match'):
                match_type_display = "编号精确匹配"
                similarity_display = "100%"
            elif match_type == 'title_similarity':
                match_type_display = "标题相似度"
                similarity_display = f"{similarity:.1%}"
            else:
                match_type_display = match_type
                similarity_display = f"{similarity:.1%}"
            
            # 插入数据
            values = (
                i,
                title,
                matched_item.publish_date or "N/A",
                match_type_display,
                similarity_display,
                "双击打开"
            )
            
            item_id = self.tree.insert("", "end", values=values, tags=(str(i-1),))
            
            # 根据匹配类型设置不同颜色
            if match_type.startswith('reference_match'):
                self.tree.set(item_id, "匹配类型", "🎯 " + match_type_display)
            elif similarity >= 0.8:
                self.tree.set(item_id, "匹配类型", "✅ " + match_type_display)
            elif similarity >= 0.6:
                self.tree.set(item_id, "匹配类型", "🔍 " + match_type_display)
            else:
                self.tree.set(item_id, "匹配类型", "❓ " + match_type_display)
    
    def _on_item_double_click(self, event):
        """双击打开项目"""
        self._open_selected()
    
    def _on_right_click(self, event):
        """右键菜单"""
        selection = self.tree.selection()
        if not selection:
            return
        
        # 创建右键菜单
        context_menu = tk.Menu(self.dialog, tearoff=0)
        context_menu.add_command(label="打开链接", command=self._open_selected)
        context_menu.add_command(label="复制标题", command=self._copy_title)
        context_menu.add_command(label="查看详细信息", command=self._show_details)
        context_menu.add_separator()
        context_menu.add_command(label="从列表中移除", command=self._remove_selected)
        
        try:
            context_menu.tk_popup(event.x_root, event.y_root)
        finally:
            context_menu.grab_release()
    
    def _open_selected(self):
        """打开选中的项目"""
        selection = self.tree.selection()
        if not selection:
            messagebox.showwarning("提示", "请先选择一个项目")
            return
        
        item_id = selection[0]
        tags = self.tree.item(item_id, "tags")
        index = int(tags[0]) if tags else 0
        
        if 0 <= index < len(self.matched_items):
            matched_item, similarity, match_type = self.matched_items[index]
            self._open_url(matched_item.url)
            self.status_var.set(f"已打开: {matched_item.title[:50]}...")
    
    def _copy_title(self):
        """复制标题到剪贴板"""
        selection = self.tree.selection()
        if not selection:
            return
        
        item_id = selection[0]
        tags = self.tree.item(item_id, "tags")
        index = int(tags[0]) if tags else 0
        
        if 0 <= index < len(self.matched_items):
            matched_item, _, _ = self.matched_items[index]
            self.dialog.clipboard_clear()
            self.dialog.clipboard_append(matched_item.title)
            self.status_var.set("标题已复制到剪贴板")
    
    def _show_details(self):
        """显示详细信息"""
        selection = self.tree.selection()
        if not selection:
            return
        
        item_id = selection[0]
        tags = self.tree.item(item_id, "tags")
        index = int(tags[0]) if tags else 0
        
        if 0 <= index < len(self.matched_items):
            matched_item, similarity, match_type = self.matched_items[index]
            
            details = f"""项目详细信息:
            
标题: {matched_item.title}
发布日期: {matched_item.publish_date}
平台: {matched_item.platform_id}
公告类型: {matched_item.announcement_type}
匹配相似度: {similarity:.1%}
匹配类型: {match_type}

编号信息:
招标编号: {matched_item.tender_reference or '无'}
公示编号: {matched_item.publicity_number or '无'}
登记编号: {matched_item.registration_id or '无'}
标段编号: {matched_item.section_number or '无'}

URL: {matched_item.url}"""
            
            messagebox.showinfo("项目详细信息", details)
    
    def _remove_selected(self):
        """从列表中移除选中项目"""
        selection = self.tree.selection()
        if not selection:
            return
        
        if messagebox.askyesno("确认", "确定要从列表中移除这个项目吗？"):
            item_id = selection[0]
            tags = self.tree.item(item_id, "tags")
            index = int(tags[0]) if tags else 0
            
            if 0 <= index < len(self.matched_items):
                removed_item = self.matched_items.pop(index)
                self.tree.delete(item_id)
                
                # 重新编号
                for i, item_id in enumerate(self.tree.get_children()):
                    self.tree.item(item_id, tags=(str(i),))
                    self.tree.set(item_id, "序号", str(i + 1))
                
                self.status_var.set(f"已移除项目，剩余 {len(self.matched_items)} 个")
    
    def _show_reference_association(self):
        """显示编号关联分析"""
        # 分析当前项目和匹配项目的编号关联
        current_refs = self._get_item_references(self.current_item)
        
        analysis = f"编号关联分析:\n\n当前项目编号:\n"
        for field, value in current_refs.items():
            if value:
                analysis += f"  {field}: {value}\n"
        
        analysis += "\n匹配项目编号关联:\n"
        
        for i, (matched_item, similarity, match_type) in enumerate(self.matched_items, 1):
            matched_refs = self._get_item_references(matched_item)
            analysis += f"\n项目 {i}:\n"
            
            # 找出共同编号
            common_refs = []
            for field, current_val in current_refs.items():
                matched_val = matched_refs.get(field)
                if current_val and matched_val and current_val == matched_val:
                    common_refs.append(f"{field}: {current_val}")
            
            if common_refs:
                analysis += f"  共同编号: {', '.join(common_refs)}\n"
            else:
                analysis += f"  无共同编号\n"
            
            for field, value in matched_refs.items():
                if value:
                    analysis += f"  {field}: {value}\n"
        
        messagebox.showinfo("编号关联分析", analysis)
    
    def _get_item_references(self, item: BiddingData) -> dict:
        """获取项目的所有编号"""
        return {
            "招标编号": item.tender_reference or "",
            "公示编号": item.publicity_number or "",
            "登记编号": item.registration_id or "",
            "标段编号": item.section_number or ""
        }
    
    def _open_url(self, url: str):
        """打开URL"""
        try:
            webbrowser.open(url)
            print(f"已在浏览器中打开: {url}")
        except Exception as e:
            messagebox.showerror("错误", f"无法打开浏览器: {e}")
