#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
测试UI改进效果的演示脚本
"""

import tkinter as tk
import customtkinter
import time
import threading
from typing import Dict

class UIImprovementDemo:
    """UI改进效果演示"""
    
    def __init__(self):
        # 设置主题
        customtkinter.set_appearance_mode("light")
        customtkinter.set_default_color_theme("blue")
        
        # 创建主窗口
        self.root = customtkinter.CTk()
        self.root.title("招投标数据爬取系统 - UI改进演示")
        self.root.geometry("800x600")
        
        # 初始化组件
        self.status_indicators = {}
        self.progress_bars = {}
        self.eta_labels = {}
        
        self.create_widgets()
        
    def create_widgets(self):
        """创建界面组件"""
        # 标题
        title_label = customtkinter.CTkLabel(
            self.root,
            text="🎨 UI改进效果演示",
            font=customtkinter.CTkFont(family="Microsoft YaHei UI", size=24, weight="bold")
        )
        title_label.pack(pady=20)
        
        # 说明文字
        desc_label = customtkinter.CTkLabel(
            self.root,
            text="演示新增的状态指示器和彩色进度显示功能",
            font=customtkinter.CTkFont(family="Microsoft YaHei UI", size=14),
            text_color="#666666"
        )
        desc_label.pack(pady=(0, 30))
        
        # 平台列表框架
        platforms_frame = customtkinter.CTkFrame(self.root)
        platforms_frame.pack(padx=40, pady=20, fill="both", expand=True)
        
        # 平台标题
        platforms_title = customtkinter.CTkLabel(
            platforms_frame,
            text="📊 平台状态监控",
            font=customtkinter.CTkFont(family="Microsoft YaHei UI", size=18, weight="bold")
        )
        platforms_title.pack(pady=(20, 15))
        
        # 创建演示平台
        platforms = [
            ("cnzhaobiao", "全国招标信息网"),
            ("hbbidcloud", "湖北招投标云平台"),
            ("whzbtb", "武汉招投标网")
        ]
        
        for i, (platform_id, platform_name) in enumerate(platforms):
            # 平台行框架
            platform_frame = customtkinter.CTkFrame(platforms_frame, fg_color="transparent")
            platform_frame.pack(fill="x", padx=20, pady=8)
            
            # 平台名称
            name_label = customtkinter.CTkLabel(
                platform_frame,
                text=platform_name,
                font=customtkinter.CTkFont(family="Microsoft YaHei UI", size=14),
                width=200
            )
            name_label.pack(side="left", padx=(0, 10))
            
            # 状态指示器
            status_indicator = customtkinter.CTkLabel(
                platform_frame,
                text="●",
                font=customtkinter.CTkFont(family="Microsoft YaHei UI", size=16),
                text_color="#6c757d",  # 默认灰色
                width=20
            )
            status_indicator.pack(side="left", padx=(0, 10))
            self.status_indicators[platform_id] = status_indicator
            
            # 进度条
            progress_bar = customtkinter.CTkProgressBar(
                platform_frame,
                width=300,
                height=20,
                progress_color="#28a745",
                fg_color="#e9ecef"
            )
            progress_bar.set(0)
            progress_bar.pack(side="left", padx=(0, 10))
            self.progress_bars[platform_id] = progress_bar
            
            # 状态标签
            eta_label = customtkinter.CTkLabel(
                platform_frame,
                text="⏸️ 待机中",
                font=customtkinter.CTkFont(family="Microsoft YaHei UI", size=11),
                text_color="#6c757d"
            )
            eta_label.pack(side="left", padx=(0, 10))
            self.eta_labels[platform_id] = eta_label
        
        # 控制按钮框架
        button_frame = customtkinter.CTkFrame(self.root, fg_color="transparent")
        button_frame.pack(pady=20)
        
        # 演示按钮
        demo_button = customtkinter.CTkButton(
            button_frame,
            text="🚀 开始演示",
            font=customtkinter.CTkFont(family="Microsoft YaHei UI", size=14),
            command=self.start_demo,
            width=120,
            height=35
        )
        demo_button.pack(side="left", padx=10)
        
        # 重置按钮
        reset_button = customtkinter.CTkButton(
            button_frame,
            text="🔄 重置状态",
            font=customtkinter.CTkFont(family="Microsoft YaHei UI", size=14),
            command=self.reset_demo,
            width=120,
            height=35,
            fg_color="#6c757d"
        )
        reset_button.pack(side="left", padx=10)
        
        # 状态说明框架
        legend_frame = customtkinter.CTkFrame(self.root)
        legend_frame.pack(padx=40, pady=(0, 20), fill="x")
        
        legend_title = customtkinter.CTkLabel(
            legend_frame,
            text="🎨 状态颜色说明",
            font=customtkinter.CTkFont(family="Microsoft YaHei UI", size=16, weight="bold")
        )
        legend_title.pack(pady=(15, 10))
        
        # 颜色说明
        legend_container = customtkinter.CTkFrame(legend_frame, fg_color="transparent")
        legend_container.pack(pady=(0, 15))
        
        legends = [
            ("●", "#6c757d", "待机"),
            ("●", "#ffc107", "准备中"),
            ("●", "#007bff", "运行中"),
            ("●", "#28a745", "完成"),
            ("●", "#dc3545", "错误"),
            ("●", "#fd7e14", "已停止")
        ]
        
        for symbol, color, desc in legends:
            legend_item = customtkinter.CTkFrame(legend_container, fg_color="transparent")
            legend_item.pack(side="left", padx=15)
            
            symbol_label = customtkinter.CTkLabel(
                legend_item,
                text=symbol,
                font=customtkinter.CTkFont(family="Microsoft YaHei UI", size=16),
                text_color=color
            )
            symbol_label.pack(side="left")
            
            desc_label = customtkinter.CTkLabel(
                legend_item,
                text=desc,
                font=customtkinter.CTkFont(family="Microsoft YaHei UI", size=12),
                text_color="#495057"
            )
            desc_label.pack(side="left", padx=(5, 0))
    
    def update_platform_status(self, platform_id: str, status: str, message: str = "", progress: float = 0):
        """更新平台状态"""
        if platform_id not in self.status_indicators:
            return
        
        # 状态颜色映射
        status_colors = {
            'waiting': "#6c757d",    # 灰色 - 待机
            'preparing': "#ffc107",  # 黄色 - 准备中
            'running': "#007bff",    # 蓝色 - 运行中
            'completed': "#28a745",  # 绿色 - 完成
            'error': "#dc3545",      # 红色 - 错误
            'stopped': "#fd7e14"     # 橙色 - 已停止
        }
        
        # 状态图标映射
        status_icons = {
            'waiting': "⏸️",
            'preparing': "🔄",
            'running': "▶️",
            'completed': "✅",
            'error': "❌",
            'stopped': "⏹️"
        }
        
        # 更新状态指示器颜色
        color = status_colors.get(status, "#6c757d")
        self.status_indicators[platform_id].configure(text_color=color)
        
        # 更新进度条
        self.progress_bars[platform_id].set(progress)
        
        # 更新状态消息
        icon = status_icons.get(status, "")
        display_message = message if message else status
        self.eta_labels[platform_id].configure(
            text=f"{icon} {display_message}",
            text_color=color
        )
    
    def start_demo(self):
        """开始演示"""
        def demo_sequence():
            platforms = ["cnzhaobiao", "hbbidcloud", "whzbtb"]
            
            # 阶段1: 准备中
            for platform_id in platforms:
                self.update_platform_status(platform_id, 'preparing', '正在初始化...', 0)
                time.sleep(0.5)
            
            # 阶段2: 运行中
            for i, platform_id in enumerate(platforms):
                for progress in range(0, 101, 10):
                    self.update_platform_status(
                        platform_id, 
                        'running', 
                        f'第{progress//10 + 1}页 ({progress}%) | 新增{progress//5}条',
                        progress / 100
                    )
                    time.sleep(0.2)
                
                # 完成
                self.update_platform_status(platform_id, 'completed', '任务完成', 1.0)
                time.sleep(0.5)
        
        # 在后台线程运行演示
        threading.Thread(target=demo_sequence, daemon=True).start()
    
    def reset_demo(self):
        """重置演示状态"""
        platforms = ["cnzhaobiao", "hbbidcloud", "whzbtb"]
        for platform_id in platforms:
            self.update_platform_status(platform_id, 'waiting', '待机中', 0)
    
    def run(self):
        """运行演示"""
        self.root.mainloop()

if __name__ == "__main__":
    demo = UIImprovementDemo()
    demo.run()
