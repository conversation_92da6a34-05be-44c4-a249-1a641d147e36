#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
测试新版武汉爬虫是否能被插件加载器正确识别
"""

import sys
import os

# 添加项目根目录到路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from gui.plugin_loader import PluginLoader

def test_plugin_recognition():
    """测试插件识别"""
    print("🔍 测试新版武汉爬虫插件识别")
    print("=" * 60)
    
    # 创建插件加载器
    loader = PluginLoader()
    
    # 加载所有爬虫
    scrapers = loader.load_scrapers()
    
    print(f"📊 加载结果:")
    print(f"  总共加载爬虫数量: {len(scrapers)}")
    
    # 检查武汉爬虫
    whzbtb_scrapers = []
    for platform_id, scraper in scrapers.items():
        if platform_id == 'whzbtb':
            whzbtb_scrapers.append(scraper)
            print(f"\n✅ 发现武汉爬虫:")
            print(f"  平台ID: {platform_id}")
            print(f"  平台名称: {scraper.platform_name}")
            print(f"  类名: {scraper.__class__.__name__}")
            print(f"  模块: {scraper.__class__.__module__}")
            print(f"  基础URL: {getattr(scraper, 'base_url', 'N/A')}")
    
    if not whzbtb_scrapers:
        print("\n❌ 未发现武汉爬虫")
        return False
    
    if len(whzbtb_scrapers) > 1:
        print(f"\n⚠️ 发现多个武汉爬虫实例: {len(whzbtb_scrapers)}")
        print("这可能导致冲突，建议检查:")
        for scraper in whzbtb_scrapers:
            print(f"  - {scraper.__class__.__name__} ({scraper.__class__.__module__})")
    
    # 显示所有爬虫信息
    print(f"\n📋 所有已加载的爬虫:")
    for platform_id, scraper in scrapers.items():
        print(f"  {platform_id}: {scraper.platform_name} ({scraper.__class__.__name__})")
    
    # 验证爬虫
    print(f"\n🔧 验证爬虫:")
    for platform_id, scraper in scrapers.items():
        is_valid, errors = loader.validate_scraper(scraper)
        status = "✅ 有效" if is_valid else f"❌ 无效: {'; '.join(errors)}"
        print(f"  {platform_id}: {status}")
    
    # 检查是否有冲突
    print(f"\n🔍 检查潜在冲突:")
    
    # 检查文件冲突
    scrapers_dir = "scrapers"
    wuhan_files = []
    for filename in os.listdir(scrapers_dir):
        if filename.startswith("whzbtb") and filename.endswith(".py"):
            wuhan_files.append(filename)
    
    print(f"  武汉爬虫相关文件: {wuhan_files}")
    
    if len(wuhan_files) > 1:
        print(f"  ⚠️ 发现多个武汉爬虫文件，可能导致冲突:")
        for file in wuhan_files:
            print(f"    - {file}")
        print(f"  建议:")
        print(f"    1. 备份原文件: whzbtb_hybrid_scraper.py -> whzbtb_hybrid_scraper_old.py")
        print(f"    2. 替换为新版本: whzbtb_hybrid_scraper_new.py -> whzbtb_hybrid_scraper.py")
        print(f"    3. 删除临时文件: whzbtb_hybrid_scraper_new.py")
    
    return len(whzbtb_scrapers) == 1

def test_gui_integration():
    """测试GUI集成"""
    print(f"\n🖥️ 测试GUI集成")
    print("=" * 60)
    
    try:
        from gui.task_manager import TaskManager
        import queue
        
        # 创建任务管理器
        message_queue = queue.Queue()
        task_manager = TaskManager(message_queue)
        
        # 获取可用平台
        platforms = task_manager.get_available_platforms()
        
        print(f"📊 GUI可识别的平台:")
        for platform_id, platform_name in platforms.items():
            print(f"  {platform_id}: {platform_name}")
        
        # 检查武汉爬虫
        if 'whzbtb' in platforms:
            print(f"\n✅ GUI成功识别武汉爬虫:")
            print(f"  平台ID: whzbtb")
            print(f"  显示名称: {platforms['whzbtb']}")
            
            # 获取爬虫实例
            scraper = task_manager.scrapers.get('whzbtb')
            if scraper:
                print(f"  爬虫类: {scraper.__class__.__name__}")
                print(f"  模块: {scraper.__class__.__module__}")
                
                # 检查关键方法
                methods_to_check = ['scrape_list_async', 'extract_details', 'get_total_items']
                print(f"  关键方法检查:")
                for method_name in methods_to_check:
                    has_method = hasattr(scraper, method_name)
                    print(f"    {method_name}: {'✅' if has_method else '❌'}")
            
            return True
        else:
            print(f"\n❌ GUI未能识别武汉爬虫")
            return False
            
    except Exception as e:
        print(f"\n❌ GUI集成测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """主测试函数"""
    print("🚀 新版武汉爬虫插件识别测试")
    print("=" * 80)
    
    # 测试插件识别
    plugin_ok = test_plugin_recognition()
    
    # 测试GUI集成
    gui_ok = test_gui_integration()
    
    print(f"\n" + "=" * 80)
    print("📋 测试总结:")
    print("=" * 80)
    
    print(f"插件加载器识别: {'✅ 成功' if plugin_ok else '❌ 失败'}")
    print(f"GUI任务管理器集成: {'✅ 成功' if gui_ok else '❌ 失败'}")
    
    if plugin_ok and gui_ok:
        print(f"\n🎉 新版武汉爬虫可以被GUI正确识别和加载！")
        print(f"\n📝 建议操作:")
        print(f"  1. 备份原文件: mv whzbtb_hybrid_scraper.py whzbtb_hybrid_scraper_old.py")
        print(f"  2. 替换新版本: mv whzbtb_hybrid_scraper_new.py whzbtb_hybrid_scraper.py")
        print(f"  3. 重启GUI应用以加载新版本")
    else:
        print(f"\n⚠️ 存在问题，需要进一步调试:")
        if not plugin_ok:
            print(f"  - 插件加载器无法正确识别新版武汉爬虫")
        if not gui_ok:
            print(f"  - GUI任务管理器无法正确集成新版武汉爬虫")

if __name__ == "__main__":
    main()
