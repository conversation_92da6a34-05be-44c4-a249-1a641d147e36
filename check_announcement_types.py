# check_announcement_types.py

"""
检查数据库中的公告类型分布
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from core.db_manager import DatabaseManager

def check_announcement_types():
    """检查数据库中的公告类型分布"""
    print("🔍 检查数据库中的公告类型分布")
    print("=" * 60)
    
    db = DatabaseManager()
    
    # 1. 总体公告类型统计
    print("📊 总体公告类型统计:")
    
    type_sql = """
    SELECT announcement_type, COUNT(*) as count
    FROM bidding_data 
    GROUP BY announcement_type 
    ORDER BY count DESC
    """
    db.cursor.execute(type_sql)
    type_stats = db.cursor.fetchall()
    
    total_count = sum(count for _, count in type_stats)
    
    for announcement_type, count in type_stats:
        percentage = (count / total_count) * 100 if total_count > 0 else 0
        type_display = announcement_type if announcement_type else "空值/未设置"
        print(f"  • {type_display}: {count:,} 条 ({percentage:.1f}%)")
    
    # 2. 按平台分组的公告类型统计
    print(f"\n📋 按平台分组的公告类型统计:")
    
    platform_type_sql = """
    SELECT platform_id, announcement_type, COUNT(*) as count
    FROM bidding_data 
    GROUP BY platform_id, announcement_type 
    ORDER BY platform_id, count DESC
    """
    db.cursor.execute(platform_type_sql)
    platform_type_stats = db.cursor.fetchall()
    
    current_platform = None
    for platform_id, announcement_type, count in platform_type_stats:
        if platform_id != current_platform:
            print(f"\n  🏢 平台 {platform_id}:")
            current_platform = platform_id
        
        type_display = announcement_type if announcement_type else "空值/未设置"
        print(f"    - {type_display}: {count:,} 条")
    
    # 3. 检查招标数据的存在情况
    print(f"\n🔍 招标数据检查:")
    
    # 检查明确标记为"招标"的数据
    tender_sql = "SELECT COUNT(*) FROM bidding_data WHERE announcement_type = '招标'"
    db.cursor.execute(tender_sql)
    tender_count = db.cursor.fetchone()[0]
    print(f"  • 明确标记为'招标'的数据: {tender_count:,} 条")
    
    # 检查标题中包含招标关键词的数据
    tender_title_sql = """
    SELECT COUNT(*) FROM bidding_data 
    WHERE (title LIKE '%招标%' OR title LIKE '%采购%' OR title LIKE '%询价%') 
    AND announcement_type != '招标'
    """
    db.cursor.execute(tender_title_sql)
    tender_title_count = db.cursor.fetchone()[0]
    print(f"  • 标题包含招标关键词但未标记为招标的数据: {tender_title_count:,} 条")
    
    # 4. 样本数据展示
    print(f"\n📄 样本数据展示:")
    
    # 显示不同类型的样本
    for announcement_type, _ in type_stats[:3]:  # 只显示前3种类型
        type_display = announcement_type if announcement_type else "空值/未设置"
        print(f"\n🔸 {type_display} 样本数据 (前3条):")
        
        sample_sql = """
        SELECT platform_id, title, url, publish_date
        FROM bidding_data 
        WHERE announcement_type = ? OR (announcement_type IS NULL AND ? = '')
        ORDER BY publish_date DESC 
        LIMIT 3
        """
        
        db.cursor.execute(sample_sql, (announcement_type, announcement_type or ''))
        samples = db.cursor.fetchall()
        
        for i, (platform_id, title, url, publish_date) in enumerate(samples, 1):
            print(f"  {i}. 【{platform_id}】{title[:60]}...")
            print(f"     发布日期: {publish_date}")
            print(f"     URL: {url}")
            print()
    
    # 5. 检查是否有招标相关的URL模式
    print(f"🔍 URL模式分析:")
    
    # 检查URL中包含招标相关路径的数据
    url_patterns = [
        ('%gonggao%', '公告'),
        ('%tender%', 'tender'),
        ('%招标%', '招标'),
        ('%采购%', '采购'),
        ('%gongshi%', '公示'),
        ('%中标%', '中标')
    ]
    
    for pattern, description in url_patterns:
        pattern_sql = "SELECT COUNT(*) FROM bidding_data WHERE url LIKE ?"
        db.cursor.execute(pattern_sql, (pattern,))
        pattern_count = db.cursor.fetchone()[0]
        if pattern_count > 0:
            print(f"  • URL包含'{description}'的数据: {pattern_count:,} 条")
    
    db.close()
    
    print(f"\n" + "=" * 60)
    print("✅ 公告类型检查完成！")
    print("=" * 60)

if __name__ == "__main__":
    check_announcement_types()
