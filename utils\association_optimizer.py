#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
关联优化器
提供智能的关联建议和优化策略
"""

import re
from typing import List, Dict, Tuple, Optional
from models.data_model import BiddingData
from core.db_manager import DatabaseManager


class AssociationOptimizer:
    """关联优化器"""
    
    @classmethod
    def analyze_association_potential(cls, data_list: List[BiddingData]) -> Dict:
        """分析关联潜力"""
        analysis = {
            'total_items': len(data_list),
            'tender_items': 0,
            'bid_items': 0,
            'reference_coverage': {},
            'title_patterns': {},
            'suggestions': []
        }
        
        # 统计基本信息
        for item in data_list:
            if item.announcement_type == '招标':
                analysis['tender_items'] += 1
            else:
                analysis['bid_items'] += 1
        
        # 分析编号字段覆盖率
        reference_fields = ['tender_reference', 'publicity_number', 'registration_id', 'section_number']
        for field in reference_fields:
            filled_count = sum(1 for item in data_list if getattr(item, field, '').strip())
            analysis['reference_coverage'][field] = {
                'count': filled_count,
                'percentage': (filled_count / len(data_list) * 100) if data_list else 0
            }
        
        # 分析标题模式
        analysis['title_patterns'] = cls._analyze_title_patterns(data_list)
        
        # 生成优化建议
        analysis['suggestions'] = cls._generate_suggestions(analysis)
        
        return analysis
    
    @classmethod
    def _analyze_title_patterns(cls, data_list: List[BiddingData]) -> Dict:
        """分析标题模式"""
        patterns = {
            'common_keywords': {},
            'project_names': {},
            'announcement_indicators': {}
        }
        
        # 常见关键词统计
        common_keywords = ['建设', '工程', '采购', '服务', '设备', '施工', '监理', '设计']
        for keyword in common_keywords:
            count = sum(1 for item in data_list if keyword in item.title)
            if count > 0:
                patterns['common_keywords'][keyword] = count
        
        # 公告类型指示词
        tender_indicators = ['招标', '采购', '询价', '竞价']
        bid_indicators = ['中标', '成交', '结果', '公示']
        
        for indicator in tender_indicators:
            count = sum(1 for item in data_list if indicator in item.title)
            if count > 0:
                patterns['announcement_indicators'][f'招标_{indicator}'] = count
        
        for indicator in bid_indicators:
            count = sum(1 for item in data_list if indicator in item.title)
            if count > 0:
                patterns['announcement_indicators'][f'中标_{indicator}'] = count
        
        return patterns
    
    @classmethod
    def _generate_suggestions(cls, analysis: Dict) -> List[str]:
        """生成优化建议"""
        suggestions = []
        
        # 数据平衡建议
        tender_count = analysis['tender_items']
        bid_count = analysis['bid_items']
        
        if tender_count == 0:
            suggestions.append("🔍 建议: 数据库中缺少招标公告，建议爬取招标数据以实现完整关联")
        elif bid_count == 0:
            suggestions.append("🔍 建议: 数据库中缺少中标公告，建议爬取中标数据以实现完整关联")
        elif abs(tender_count - bid_count) > max(tender_count, bid_count) * 0.5:
            suggestions.append(f"⚖️ 建议: 数据不平衡（招标:{tender_count}, 中标:{bid_count}），建议补充数据")
        
        # 编号字段建议
        ref_coverage = analysis['reference_coverage']
        low_coverage_fields = [field for field, info in ref_coverage.items() 
                              if info['percentage'] < 20]
        
        if low_coverage_fields:
            suggestions.append(f"📋 建议: 以下编号字段填充率较低，建议优化提取算法: {', '.join(low_coverage_fields)}")
        
        # 最有价值的编号字段
        best_field = max(ref_coverage.items(), key=lambda x: x[1]['percentage'])
        if best_field[1]['percentage'] > 50:
            suggestions.append(f"✅ 优势: {best_field[0]}字段填充率较高({best_field[1]['percentage']:.1f}%)，可作为主要关联字段")
        
        # 标题模式建议
        title_patterns = analysis['title_patterns']
        common_keywords = title_patterns.get('common_keywords', {})
        
        if common_keywords:
            top_keywords = sorted(common_keywords.items(), key=lambda x: x[1], reverse=True)[:3]
            suggestions.append(f"🏷️ 发现: 常见项目关键词: {', '.join([f'{k}({v}次)' for k, v in top_keywords])}")
        
        return suggestions
    
    @classmethod
    def suggest_association_improvements(cls, db_path: str = None) -> Dict:
        """基于数据库数据提供关联改进建议"""
        db = DatabaseManager(db_path) if db_path else DatabaseManager()
        
        try:
            # 获取样本数据进行分析
            sample_sql = """
            SELECT * FROM bidding_data 
            ORDER BY scraped_timestamp DESC 
            LIMIT 1000
            """
            
            db.cursor.execute(sample_sql)
            rows = db.cursor.fetchall()
            
            if not rows:
                return {'error': '数据库中没有数据'}
            
            # 转换为BiddingData对象
            column_names = [description[0] for description in db.cursor.description]
            sample_data = []
            
            for row in rows:
                row_dict = dict(zip(column_names, row))
                row_dict['db_id'] = row_dict.get('db_id')
                sample_data.append(BiddingData(**row_dict))
            
            # 进行分析
            analysis = cls.analyze_association_potential(sample_data)
            
            # 添加数据库统计信息
            db.cursor.execute("SELECT COUNT(*) FROM bidding_data")
            total_count = db.cursor.fetchone()[0]
            
            db.cursor.execute("SELECT announcement_type, COUNT(*) FROM bidding_data GROUP BY announcement_type")
            type_stats = db.cursor.fetchall()
            
            analysis['database_stats'] = {
                'total_records': total_count,
                'type_distribution': dict(type_stats),
                'sample_size': len(sample_data)
            }
            
            return analysis
            
        except Exception as e:
            return {'error': f'分析失败: {e}'}
        finally:
            db.close()
    
    @classmethod
    def optimize_reference_extraction(cls, text: str) -> Dict[str, str]:
        """优化编号提取"""
        results = {}
        
        # 招标编号模式
        tender_patterns = [
            r'招标编号[：:]\s*([A-Za-z0-9\-_/]+)',
            r'项目编号[：:]\s*([A-Za-z0-9\-_/]+)',
            r'采购编号[：:]\s*([A-Za-z0-9\-_/]+)',
            r'编号[：:]\s*([A-Za-z0-9\-_/]+)',
        ]
        
        for pattern in tender_patterns:
            match = re.search(pattern, text)
            if match:
                results['tender_reference'] = match.group(1).strip()
                break
        
        # 公示编号模式
        publicity_patterns = [
            r'公示编号[：:]\s*([A-Za-z0-9\-_/]+)',
            r'中标公示编号[：:]\s*([A-Za-z0-9\-_/]+)',
            r'成交公示编号[：:]\s*([A-Za-z0-9\-_/]+)',
        ]
        
        for pattern in publicity_patterns:
            match = re.search(pattern, text)
            if match:
                results['publicity_number'] = match.group(1).strip()
                break
        
        # 登记编号模式
        registration_patterns = [
            r'登记编号[：:]\s*([A-Za-z0-9\-_/]+)',
            r'备案编号[：:]\s*([A-Za-z0-9\-_/]+)',
            r'项目登记编号[：:]\s*([A-Za-z0-9\-_/]+)',
        ]
        
        for pattern in registration_patterns:
            match = re.search(pattern, text)
            if match:
                results['registration_id'] = match.group(1).strip()
                break
        
        # 标段编号模式
        section_patterns = [
            r'标段[：:]?\s*([A-Za-z0-9\-_/]+)',
            r'包号[：:]\s*([A-Za-z0-9\-_/]+)',
            r'标段编号[：:]\s*([A-Za-z0-9\-_/]+)',
        ]
        
        for pattern in section_patterns:
            match = re.search(pattern, text)
            if match:
                results['section_number'] = match.group(1).strip()
                break
        
        return results
    
    @classmethod
    def calculate_association_score(cls, item1: BiddingData, item2: BiddingData) -> Tuple[float, Dict]:
        """计算两个项目的关联分数"""
        score_details = {
            'reference_match': 0,
            'title_similarity': 0,
            'date_proximity': 0,
            'platform_match': 0,
            'total_score': 0
        }
        
        # 1. 编号匹配 (权重: 40%)
        ref_score = cls._calculate_reference_score(item1, item2)
        score_details['reference_match'] = ref_score * 0.4
        
        # 2. 标题相似度 (权重: 30%)
        from utils.project_matcher import ProjectMatcher
        title_sim, _ = ProjectMatcher.calculate_similarity(item1.title, item2.title)
        score_details['title_similarity'] = title_sim * 0.3
        
        # 3. 日期接近度 (权重: 20%)
        date_score = cls._calculate_date_proximity(item1.publish_date, item2.publish_date)
        score_details['date_proximity'] = date_score * 0.2
        
        # 4. 平台匹配 (权重: 10%)
        platform_score = 1.0 if item1.platform_id == item2.platform_id else 0.0
        score_details['platform_match'] = platform_score * 0.1
        
        # 总分
        total_score = sum(score_details.values())
        score_details['total_score'] = total_score
        
        return total_score, score_details
    
    @classmethod
    def _calculate_reference_score(cls, item1: BiddingData, item2: BiddingData) -> float:
        """计算编号匹配分数"""
        ref_fields = ['tender_reference', 'publicity_number', 'registration_id', 'section_number']
        matches = 0
        total_fields = 0
        
        for field in ref_fields:
            val1 = getattr(item1, field, '').strip()
            val2 = getattr(item2, field, '').strip()
            
            if val1 and val2:
                total_fields += 1
                if val1 == val2:
                    matches += 1
        
        return matches / total_fields if total_fields > 0 else 0
    
    @classmethod
    def _calculate_date_proximity(cls, date1: str, date2: str) -> float:
        """计算日期接近度分数"""
        if not date1 or not date2:
            return 0
        
        try:
            from datetime import datetime
            d1 = datetime.strptime(date1, '%Y-%m-%d')
            d2 = datetime.strptime(date2, '%Y-%m-%d')
            
            # 计算天数差异
            days_diff = abs((d1 - d2).days)
            
            # 转换为分数 (30天内为满分，90天后为0分)
            if days_diff <= 30:
                return 1.0
            elif days_diff <= 90:
                return 1.0 - (days_diff - 30) / 60
            else:
                return 0.0
                
        except:
            return 0
