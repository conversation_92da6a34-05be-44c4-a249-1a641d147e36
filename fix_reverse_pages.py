#!/usr/bin/env python3
"""
修复倒序页码混合问题
清理数据库中混合的招标/中标倒序页码数据
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from core.db_manager import DatabaseManager
from scrapers.cnzhaobiao_scraper import CnzhaobiaoScraper
from scrapers.hbbidcloud_scraper import HbbidcloudScraper

def fix_reverse_pages():
    """修复倒序页码混合问题"""
    print("🔧 开始修复倒序页码混合问题...")
    
    db = DatabaseManager()
    
    try:
        # 获取当前各平台的总页数
        print("\n📊 获取当前各平台总页数...")
        import asyncio
        
        async def get_all_pages():
            # 中国招标信息发布网
            cnzhaobiao_scraper = CnzhaobiaoScraper()
            cn_zhongbiao = await cnzhaobiao_scraper.get_max_pages('中标')
            cn_zhaobiao = await cnzhaobiao_scraper.get_max_pages('招标')
            
            # 湖北平台
            hbbid_scraper = HbbidcloudScraper()
            hb_zhongbiao = await hbbid_scraper.get_max_pages('中标')
            hb_zhaobiao = await hbbid_scraper.get_max_pages('招标')
            
            return {
                'cnzhaobiao': {'中标': cn_zhongbiao, '招标': cn_zhaobiao},
                'hbbidcloud': {'中标': hb_zhongbiao, '招标': hb_zhaobiao}
            }
        
        page_info = asyncio.run(get_all_pages())
        
        for platform_id, types in page_info.items():
            print(f"{platform_id}:")
            for ann_type, pages in types.items():
                print(f"  {ann_type}: {pages}页")
        
        # 修复每个平台的数据
        for platform_id, types in page_info.items():
            print(f"\n🔧 修复 {platform_id} 平台数据...")
            
            for ann_type, max_pages in types.items():
                if max_pages <= 0:
                    continue
                
                # 查找异常的倒序页码
                query_sql = """
                SELECT db_id, reverse_page_number, title
                FROM bidding_data 
                WHERE platform_id = ? AND announcement_type = ? 
                AND reverse_page_number > ?
                """
                
                db.cursor.execute(query_sql, (platform_id, ann_type, max_pages))
                invalid_records = db.cursor.fetchall()
                
                if invalid_records:
                    print(f"  发现 {len(invalid_records)} 条 {ann_type} 异常倒序页码记录")
                    
                    # 选择修复策略
                    print(f"  修复策略选择:")
                    print(f"  1. 清空异常记录的倒序页码（推荐）")
                    print(f"  2. 删除异常记录")
                    print(f"  3. 跳过此类型")
                    
                    choice = input(f"  请选择修复 {platform_id}-{ann_type} 的策略 (1/2/3): ").strip()
                    
                    if choice == '1':
                        # 清空倒序页码
                        update_sql = """
                        UPDATE bidding_data 
                        SET reverse_page_number = NULL 
                        WHERE platform_id = ? AND announcement_type = ? 
                        AND reverse_page_number > ?
                        """
                        db.cursor.execute(update_sql, (platform_id, ann_type, max_pages))
                        db.conn.commit()
                        print(f"  ✅ 已清空 {len(invalid_records)} 条异常记录的倒序页码")
                        
                    elif choice == '2':
                        # 删除记录
                        delete_sql = """
                        DELETE FROM bidding_data 
                        WHERE platform_id = ? AND announcement_type = ? 
                        AND reverse_page_number > ?
                        """
                        db.cursor.execute(delete_sql, (platform_id, ann_type, max_pages))
                        db.conn.commit()
                        print(f"  ✅ 已删除 {len(invalid_records)} 条异常记录")
                        
                    else:
                        print(f"  ⏭️ 跳过 {platform_id}-{ann_type}")
                else:
                    print(f"  ✅ {ann_type} 数据正常，无需修复")
        
        # 验证修复结果
        print("\n✅ 验证修复结果...")
        for platform_id, types in page_info.items():
            for ann_type, max_pages in types.items():
                if max_pages <= 0:
                    continue
                
                # 检查是否还有异常数据
                query_sql = """
                SELECT COUNT(*) FROM bidding_data 
                WHERE platform_id = ? AND announcement_type = ? 
                AND reverse_page_number > ?
                """
                db.cursor.execute(query_sql, (platform_id, ann_type, max_pages))
                remaining_invalid = db.cursor.fetchone()[0]
                
                if remaining_invalid == 0:
                    print(f"✅ {platform_id}-{ann_type}: 修复完成")
                else:
                    print(f"⚠️ {platform_id}-{ann_type}: 仍有 {remaining_invalid} 条异常记录")
        
        print("\n🎉 倒序页码修复完成！")
        print("现在可以重新运行爬取任务，断点续爬功能应该能正常工作。")
        
    except Exception as e:
        print(f"❌ 修复过程中发生错误: {e}")
        import traceback
        traceback.print_exc()
    
    finally:
        db.close()

def quick_fix():
    """快速修复：清空所有异常的倒序页码"""
    print("🚀 快速修复模式：清空所有异常的倒序页码...")
    
    db = DatabaseManager()
    
    try:
        # 获取当前总页数
        import asyncio
        
        async def get_pages():
            cnzhaobiao_scraper = CnzhaobiaoScraper()
            hbbid_scraper = HbbidcloudScraper()
            
            return {
                'cnzhaobiao': {
                    '中标': await cnzhaobiao_scraper.get_max_pages('中标'),
                    '招标': await cnzhaobiao_scraper.get_max_pages('招标')
                },
                'hbbidcloud': {
                    '中标': await hbbid_scraper.get_max_pages('中标'),
                    '招标': await hbbid_scraper.get_max_pages('招标')
                }
            }
        
        page_info = asyncio.run(get_pages())
        
        total_fixed = 0
        for platform_id, types in page_info.items():
            for ann_type, max_pages in types.items():
                if max_pages <= 0:
                    continue
                
                # 清空异常的倒序页码
                update_sql = """
                UPDATE bidding_data 
                SET reverse_page_number = NULL 
                WHERE platform_id = ? AND announcement_type = ? 
                AND reverse_page_number > ?
                """
                
                db.cursor.execute(update_sql, (platform_id, ann_type, max_pages))
                fixed_count = db.cursor.rowcount
                total_fixed += fixed_count
                
                if fixed_count > 0:
                    print(f"✅ {platform_id}-{ann_type}: 清空了 {fixed_count} 条异常倒序页码")
        
        db.conn.commit()
        print(f"\n🎉 快速修复完成！总共修复了 {total_fixed} 条记录")
        
    except Exception as e:
        print(f"❌ 快速修复失败: {e}")
        import traceback
        traceback.print_exc()
    
    finally:
        db.close()

if __name__ == "__main__":
    print("倒序页码修复工具")
    print("1. 交互式修复（可选择修复策略）")
    print("2. 快速修复（直接清空异常倒序页码）")
    
    choice = input("请选择修复模式 (1/2): ").strip()
    
    if choice == '2':
        quick_fix()
    else:
        fix_reverse_pages()
