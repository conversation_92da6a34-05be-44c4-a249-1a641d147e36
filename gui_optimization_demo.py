#!/usr/bin/env python3
"""
GUI优化演示工具
展示关联逻辑和用户体验的改进效果
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

import tkinter as tk
from tkinter import ttk, messagebox
import customtkinter
from core.db_manager import DatabaseManager
from models.data_model import BiddingData
from utils.project_matcher import ProjectMatcher
from utils.association_optimizer import AssociationOptimizer

class GUIOptimizationDemo:
    """GUI优化演示"""
    
    def __init__(self):
        self.root = customtkinter.CTk()
        self.root.title("招投标关联逻辑优化演示")
        self.root.geometry("1200x800")
        
        # 设置主题
        customtkinter.set_appearance_mode("light")
        customtkinter.set_default_color_theme("blue")
        
        self.create_widgets()
        
    def create_widgets(self):
        """创建界面组件"""
        # 主标题
        title_label = customtkinter.CTkLabel(
            self.root, 
            text="🔗 招投标关联逻辑优化演示",
            font=customtkinter.CTkFont(size=24, weight="bold")
        )
        title_label.pack(pady=20)
        
        # 创建选项卡
        self.tabview = customtkinter.CTkTabview(self.root, width=1150, height=700)
        self.tabview.pack(padx=20, pady=10, fill="both", expand=True)
        
        # 添加选项卡
        self.tabview.add("关联分析")
        self.tabview.add("匹配演示")
        self.tabview.add("优化建议")
        self.tabview.add("编号提取")
        
        self.create_analysis_tab()
        self.create_demo_tab()
        self.create_suggestions_tab()
        self.create_extraction_tab()
        
    def create_analysis_tab(self):
        """创建关联分析选项卡"""
        tab = self.tabview.tab("关联分析")
        
        # 分析按钮
        analyze_btn = customtkinter.CTkButton(
            tab,
            text="🔍 分析数据库关联潜力",
            command=self.analyze_association_potential,
            font=customtkinter.CTkFont(size=14, weight="bold"),
            height=40
        )
        analyze_btn.pack(pady=20)
        
        # 结果显示区域
        self.analysis_text = customtkinter.CTkTextbox(tab, width=1100, height=600)
        self.analysis_text.pack(padx=20, pady=10, fill="both", expand=True)
        
    def create_demo_tab(self):
        """创建匹配演示选项卡"""
        tab = self.tabview.tab("匹配演示")
        
        # 控制面板
        control_frame = customtkinter.CTkFrame(tab)
        control_frame.pack(fill="x", padx=20, pady=10)
        
        # 演示按钮
        demo_btn = customtkinter.CTkButton(
            control_frame,
            text="🧪 运行匹配演示",
            command=self.run_matching_demo,
            font=customtkinter.CTkFont(size=14, weight="bold"),
            height=40
        )
        demo_btn.pack(side="left", padx=10, pady=10)
        
        # 清空按钮
        clear_btn = customtkinter.CTkButton(
            control_frame,
            text="🗑️ 清空结果",
            command=self.clear_demo_results,
            height=40
        )
        clear_btn.pack(side="left", padx=10, pady=10)
        
        # 结果显示
        self.demo_text = customtkinter.CTkTextbox(tab, width=1100, height=600)
        self.demo_text.pack(padx=20, pady=10, fill="both", expand=True)
        
    def create_suggestions_tab(self):
        """创建优化建议选项卡"""
        tab = self.tabview.tab("优化建议")
        
        # 生成建议按钮
        suggest_btn = customtkinter.CTkButton(
            tab,
            text="💡 生成优化建议",
            command=self.generate_suggestions,
            font=customtkinter.CTkFont(size=14, weight="bold"),
            height=40
        )
        suggest_btn.pack(pady=20)
        
        # 建议显示区域
        self.suggestions_text = customtkinter.CTkTextbox(tab, width=1100, height=600)
        self.suggestions_text.pack(padx=20, pady=10, fill="both", expand=True)
        
    def create_extraction_tab(self):
        """创建编号提取选项卡"""
        tab = self.tabview.tab("编号提取")
        
        # 输入区域
        input_frame = customtkinter.CTkFrame(tab)
        input_frame.pack(fill="x", padx=20, pady=10)
        
        input_label = customtkinter.CTkLabel(
            input_frame,
            text="输入测试文本:",
            font=customtkinter.CTkFont(size=14, weight="bold")
        )
        input_label.pack(anchor="w", padx=10, pady=(10, 5))
        
        self.input_text = customtkinter.CTkTextbox(input_frame, height=100)
        self.input_text.pack(fill="x", padx=10, pady=(0, 10))
        
        # 测试按钮
        test_btn = customtkinter.CTkButton(
            input_frame,
            text="🔧 测试编号提取",
            command=self.test_extraction,
            height=40
        )
        test_btn.pack(pady=10)
        
        # 预设测试按钮
        preset_btn = customtkinter.CTkButton(
            input_frame,
            text="📋 使用预设测试文本",
            command=self.load_preset_texts,
            height=40
        )
        preset_btn.pack(pady=(0, 10))
        
        # 结果显示
        self.extraction_text = customtkinter.CTkTextbox(tab, width=1100, height=400)
        self.extraction_text.pack(padx=20, pady=10, fill="both", expand=True)
        
    def analyze_association_potential(self):
        """分析关联潜力"""
        self.analysis_text.delete("1.0", "end")
        self.analysis_text.insert("1.0", "🔍 正在分析数据库关联潜力...\n\n")
        self.root.update()
        
        try:
            analysis = AssociationOptimizer.suggest_association_improvements()
            
            if 'error' in analysis:
                self.analysis_text.insert("end", f"❌ 分析失败: {analysis['error']}\n")
                return
            
            # 格式化显示结果
            result = "📊 数据库关联潜力分析报告\n"
            result += "=" * 50 + "\n\n"
            
            # 数据库统计
            db_stats = analysis.get('database_stats', {})
            result += f"📈 数据库统计:\n"
            result += f"  总记录数: {db_stats.get('total_records', 0):,} 条\n"
            result += f"  样本大小: {db_stats.get('sample_size', 0):,} 条\n\n"
            
            type_dist = db_stats.get('type_distribution', {})
            result += f"📋 公告类型分布:\n"
            for ann_type, count in type_dist.items():
                result += f"  {ann_type}: {count:,} 条\n"
            result += "\n"
            
            # 编号字段覆盖率
            result += f"🏷️ 编号字段覆盖率:\n"
            ref_coverage = analysis.get('reference_coverage', {})
            field_names = {
                'tender_reference': '招标编号',
                'publicity_number': '公示编号',
                'registration_id': '登记编号',
                'section_number': '标段编号'
            }
            
            for field, info in ref_coverage.items():
                field_name = field_names.get(field, field)
                result += f"  {field_name}: {info['count']:,} 条 ({info['percentage']:.1f}%)\n"
            result += "\n"
            
            # 标题模式分析
            title_patterns = analysis.get('title_patterns', {})
            common_keywords = title_patterns.get('common_keywords', {})
            
            if common_keywords:
                result += f"🔤 常见关键词分析:\n"
                for keyword, count in sorted(common_keywords.items(), key=lambda x: x[1], reverse=True):
                    result += f"  {keyword}: {count} 次\n"
                result += "\n"
            
            # 优化建议
            suggestions = analysis.get('suggestions', [])
            if suggestions:
                result += f"💡 系统优化建议:\n"
                for suggestion in suggestions:
                    result += f"  {suggestion}\n"
                result += "\n"
            
            # 关联质量评估
            result += f"📊 关联质量评估:\n"
            if type_dist.get('招标', 0) == 0:
                result += "  ❌ 缺少招标数据，无法进行有效关联\n"
                result += "  📈 建议: 爬取招标公告数据以实现完整关联功能\n"
            else:
                result += "  ✅ 数据类型完整，支持双向关联\n"
            
            # 最佳关联字段推荐
            best_field = max(ref_coverage.items(), key=lambda x: x[1]['percentage'])
            if best_field[1]['percentage'] > 20:
                field_name = field_names.get(best_field[0], best_field[0])
                result += f"  🎯 推荐主要关联字段: {field_name} (填充率: {best_field[1]['percentage']:.1f}%)\n"
            
            self.analysis_text.delete("1.0", "end")
            self.analysis_text.insert("1.0", result)
            
        except Exception as e:
            error_msg = f"❌ 分析过程中发生错误: {e}\n"
            self.analysis_text.insert("end", error_msg)
    
    def run_matching_demo(self):
        """运行匹配演示"""
        self.demo_text.delete("1.0", "end")
        self.demo_text.insert("1.0", "🧪 正在运行匹配算法演示...\n\n")
        self.root.update()
        
        try:
            db = DatabaseManager()
            
            # 获取测试数据
            test_sql = """
            SELECT * FROM bidding_data 
            WHERE section_number IS NOT NULL AND section_number != ''
            ORDER BY scraped_timestamp DESC 
            LIMIT 20
            """
            
            db.cursor.execute(test_sql)
            test_rows = db.cursor.fetchall()
            
            if not test_rows:
                self.demo_text.insert("end", "❌ 没有找到适合的测试数据\n")
                db.close()
                return
            
            # 转换为BiddingData对象
            column_names = [description[0] for description in db.cursor.description]
            test_data = []
            
            for row in test_rows:
                row_dict = dict(zip(column_names, row))
                row_dict['db_id'] = row_dict.get('db_id')
                test_data.append(BiddingData(**row_dict))
            
            db.close()
            
            # 运行匹配算法
            matches = ProjectMatcher.find_project_matches(test_data)
            
            # 格式化显示结果
            result = f"🎯 匹配算法演示结果\n"
            result += "=" * 40 + "\n\n"
            result += f"📊 测试数据: {len(test_data)} 个项目\n"
            result += f"🔍 匹配结果: {len(matches)} 个匹配项\n\n"
            
            # 统计匹配状态
            match_stats = {}
            for match_info in matches.values():
                status = match_info['match_status']
                match_stats[status] = match_stats.get(status, 0) + 1
            
            result += f"📈 匹配状态分布:\n"
            status_names = {
                'none': '无匹配',
                'reference_match': '编号精确匹配',
                'high_unique': '高质量唯一匹配',
                'low_or_multiple': '低质量或多重匹配'
            }
            
            for status, count in match_stats.items():
                status_name = status_names.get(status, status)
                percentage = (count / len(matches) * 100) if matches else 0
                result += f"  {status_name}: {count} 项 ({percentage:.1f}%)\n"
            
            result += "\n📋 详细匹配示例:\n"
            
            # 显示前5个匹配示例
            example_count = 0
            for item_key, match_info in matches.items():
                if example_count >= 5:
                    break
                
                current_item = match_info['current_item']
                match_status = match_info['match_status']
                match_details = match_info.get('match_details', '')
                tender_matches = match_info.get('tender_matches', [])
                
                result += f"\n示例 {example_count + 1}:\n"
                result += f"  项目: {current_item.title[:60]}...\n"
                result += f"  类型: {current_item.announcement_type}\n"
                result += f"  状态: {status_names.get(match_status, match_status)}\n"
                result += f"  详情: {match_details}\n"
                result += f"  匹配数: {len(tender_matches)} 个\n"
                
                example_count += 1
            
            self.demo_text.delete("1.0", "end")
            self.demo_text.insert("1.0", result)
            
        except Exception as e:
            error_msg = f"❌ 演示过程中发生错误: {e}\n"
            self.demo_text.insert("end", error_msg)
    
    def clear_demo_results(self):
        """清空演示结果"""
        self.demo_text.delete("1.0", "end")
        self.demo_text.insert("1.0", "📝 演示结果已清空，点击'运行匹配演示'开始新的演示\n")
    
    def generate_suggestions(self):
        """生成优化建议"""
        self.suggestions_text.delete("1.0", "end")
        self.suggestions_text.insert("1.0", "💡 正在生成优化建议...\n\n")
        self.root.update()
        
        suggestions = """🚀 招投标关联系统优化建议
========================================

📊 当前系统状态评估:
✅ 已实现: 增强的匹配算法，支持编号精确匹配和标题相似度匹配
✅ 已实现: 改进的GUI界面，提供详细的匹配信息和状态显示
✅ 已实现: 智能关联分析和优化建议系统
✅ 已实现: 双向匹配功能，支持招标->中标和中标->招标关联

🎯 短期优化目标 (1-2周):
1. 📋 数据完整性提升
   - 爬取招标公告数据，实现数据类型平衡
   - 优化编号字段提取算法，提高填充率
   - 实施数据质量检查和清理流程

2. 🔍 匹配算法优化
   - 调整相似度阈值，提高匹配准确率
   - 增加时间窗口限制，避免跨时间段的错误匹配
   - 实现平台特定的匹配规则

3. 🎨 用户体验改进
   - 增加匹配结果的可视化展示
   - 提供匹配结果的手动确认和修正功能
   - 添加匹配历史记录和统计分析

🚀 中期发展计划 (1-2个月):
1. 🤖 智能化升级
   - 引入机器学习算法，提高匹配准确率
   - 实现自适应阈值调整
   - 开发异常匹配检测和报警机制

2. 📈 性能优化
   - 优化大规模数据的匹配计算效率
   - 实现增量匹配，避免重复计算
   - 添加缓存机制，提高响应速度

3. 🔗 功能扩展
   - 支持多级关联（项目->标段->子项目）
   - 实现关联关系的图形化展示
   - 添加关联质量评分和排序功能

💡 具体实施建议:
1. 立即行动: 使用新的公告类型选择功能爬取招标数据
2. 数据优化: 分析现有编号字段，优化提取正则表达式
3. 算法调优: 基于实际匹配效果调整相似度阈值
4. 用户反馈: 收集用户使用反馈，持续改进界面体验

🎉 预期效果:
- 匹配准确率提升至85%以上
- 用户操作效率提升50%
- 数据关联完整性达到90%以上
- 系统响应速度提升30%

📞 技术支持:
如需进一步的技术支持或定制化开发，请联系开发团队。
系统将持续更新和优化，确保最佳的用户体验。"""
        
        self.suggestions_text.delete("1.0", "end")
        self.suggestions_text.insert("1.0", suggestions)
    
    def test_extraction(self):
        """测试编号提取"""
        input_text = self.input_text.get("1.0", "end-1c").strip()
        
        if not input_text:
            messagebox.showwarning("提示", "请输入测试文本")
            return
        
        try:
            extracted = AssociationOptimizer.optimize_reference_extraction(input_text)
            
            result = f"🔧 编号提取测试结果\n"
            result += "=" * 30 + "\n\n"
            result += f"📝 输入文本:\n{input_text}\n\n"
            result += f"🎯 提取结果:\n"
            
            field_names = {
                'tender_reference': '招标编号',
                'publicity_number': '公示编号',
                'registration_id': '登记编号',
                'section_number': '标段编号'
            }
            
            if extracted:
                for field, value in extracted.items():
                    field_name = field_names.get(field, field)
                    result += f"  ✅ {field_name}: {value}\n"
            else:
                result += "  ❌ 未提取到任何编号信息\n"
            
            result += "\n" + "-" * 40 + "\n"
            
            self.extraction_text.insert("end", result)
            
        except Exception as e:
            error_msg = f"❌ 提取过程中发生错误: {e}\n"
            self.extraction_text.insert("end", error_msg)
    
    def load_preset_texts(self):
        """加载预设测试文本"""
        preset_texts = [
            "项目编号：HNZB2024-001 某某工程施工招标公告",
            "招标编号: XM-2024-0156 设备采购项目招标",
            "公示编号：GS2024-089 中标结果公示",
            "标段：第一标段 登记编号：DJ-2024-012",
            "采购编号 CG2024-078 办公设备采购项目",
            "项目名称：市政道路建设工程 招标文件编号：ZB2024-156"
        ]
        
        self.input_text.delete("1.0", "end")
        self.input_text.insert("1.0", "\n".join(preset_texts))
        
        # 自动运行测试
        self.extraction_text.delete("1.0", "end")
        for text in preset_texts:
            self.input_text.delete("1.0", "end")
            self.input_text.insert("1.0", text)
            self.test_extraction()
    
    def run(self):
        """运行演示程序"""
        self.root.mainloop()

if __name__ == "__main__":
    demo = GUIOptimizationDemo()
    demo.run()
