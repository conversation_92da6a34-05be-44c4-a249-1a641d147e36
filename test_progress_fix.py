#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
测试武汉爬虫进度显示修复效果
"""

import sys
import os
import asyncio
import time

# 添加项目根目录到路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from scrapers.whzbtb_hybrid_scraper import WhzbtbHybridScraper
from core.db_manager import DatabaseManager

class MockProgressCallback:
    """模拟GUI进度回调"""
    
    def __init__(self):
        self.messages = []
        self.progress_values = []
        self.page_numbers = []
    
    def __call__(self, message, progress):
        """进度回调函数"""
        print(f"📊 进度回调: {progress:.1f}% - {message}")
        
        self.messages.append(message)
        self.progress_values.append(progress)
        
        # 尝试解析页数信息
        if "第" in message and "/" in message and "页" in message:
            try:
                page_info = message.split("第")[1].split("页")[0]
                current_page = int(page_info.split("/")[0])
                self.page_numbers.append(current_page)
                print(f"  ✅ 成功解析页数: {current_page}")
            except Exception as e:
                print(f"  ❌ 页数解析失败: {e}")
                self.page_numbers.append(0)
        else:
            self.page_numbers.append(0)
    
    def get_summary(self):
        """获取回调总结"""
        return {
            'total_callbacks': len(self.messages),
            'max_progress': max(self.progress_values) if self.progress_values else 0,
            'parsed_pages': [p for p in self.page_numbers if p > 0],
            'last_message': self.messages[-1] if self.messages else "无消息"
        }

async def test_progress_fix():
    """测试进度显示修复"""
    print("🔧 测试武汉爬虫进度显示修复")
    print("=" * 60)
    
    # 创建模拟进度回调
    progress_callback = MockProgressCallback()
    
    # 创建爬虫实例
    scraper = WhzbtbHybridScraper(headless=True, progress_callback=progress_callback)
    
    try:
        print("1️⃣ 测试招标数据进度回调...")
        
        # 创建数据库管理器
        db = DatabaseManager()
        
        # 测试招标数据爬取（只获取少量数据）
        tender_items = []
        count = 0
        
        start_time = time.time()
        async for item in scraper.scrape_list(db, '招标'):
            tender_items.append(item)
            count += 1
            if count >= 3:  # 只获取3条数据
                break
        
        elapsed_time = time.time() - start_time
        
        # 获取进度回调总结
        summary = progress_callback.get_summary()
        
        print(f"\n📋 招标数据测试结果:")
        print(f"  获取数据: {len(tender_items)} 条")
        print(f"  耗时: {elapsed_time:.1f} 秒")
        print(f"  进度回调次数: {summary['total_callbacks']}")
        print(f"  最大进度值: {summary['max_progress']:.1f}%")
        print(f"  解析到的页数: {summary['parsed_pages']}")
        print(f"  最后消息: {summary['last_message']}")
        
        # 验证进度回调是否正常
        progress_ok = (
            summary['total_callbacks'] > 0 and
            summary['max_progress'] > 0 and
            len(summary['parsed_pages']) > 0
        )
        
        print(f"  进度回调状态: {'✅ 正常' if progress_ok else '❌ 异常'}")
        
        db.close()
        
        print(f"\n2️⃣ 测试中标数据进度回调...")
        
        # 重置回调记录
        progress_callback.messages.clear()
        progress_callback.progress_values.clear()
        progress_callback.page_numbers.clear()
        
        # 测试中标数据
        db = DatabaseManager()
        winbid_items = []
        count = 0
        
        start_time = time.time()
        async for item in scraper.scrape_list(db, '中标'):
            winbid_items.append(item)
            count += 1
            if count >= 3:  # 只获取3条数据
                break
        
        elapsed_time = time.time() - start_time
        
        # 获取进度回调总结
        summary = progress_callback.get_summary()
        
        print(f"\n📋 中标数据测试结果:")
        print(f"  获取数据: {len(winbid_items)} 条")
        print(f"  耗时: {elapsed_time:.1f} 秒")
        print(f"  进度回调次数: {summary['total_callbacks']}")
        print(f"  最大进度值: {summary['max_progress']:.1f}%")
        print(f"  解析到的页数: {summary['parsed_pages']}")
        print(f"  最后消息: {summary['last_message']}")
        
        # 验证进度回调是否正常
        progress_ok2 = (
            summary['total_callbacks'] > 0 and
            summary['max_progress'] > 0 and
            len(summary['parsed_pages']) > 0
        )
        
        print(f"  进度回调状态: {'✅ 正常' if progress_ok2 else '❌ 异常'}")
        
        db.close()
        
        # 总结
        print(f"\n" + "=" * 60)
        print("📋 进度修复测试总结:")
        print("=" * 60)
        
        overall_success = progress_ok and progress_ok2 and len(tender_items) > 0 and len(winbid_items) > 0
        
        print(f"招标进度回调: {'✅ 正常' if progress_ok else '❌ 异常'}")
        print(f"中标进度回调: {'✅ 正常' if progress_ok2 else '❌ 异常'}")
        print(f"数据获取功能: {'✅ 正常' if len(tender_items) > 0 and len(winbid_items) > 0 else '❌ 异常'}")
        
        if overall_success:
            print(f"\n🎉 进度显示修复成功！")
            print(f"💡 修复效果:")
            print(f"  - 进度回调格式正确")
            print(f"  - 页数信息可正确解析")
            print(f"  - GUI进度条应能正常更新")
            print(f"  - 不再出现类型错误")
        else:
            print(f"\n⚠️ 部分功能仍需调试")
            if not progress_ok or not progress_ok2:
                print(f"  - 进度回调格式需要进一步优化")
            if len(tender_items) == 0 or len(winbid_items) == 0:
                print(f"  - 数据获取功能需要检查")
        
        return overall_success
        
    except Exception as e:
        print(f"❌ 测试过程中出现错误: {e}")
        import traceback
        traceback.print_exc()
        return False
    
    finally:
        # 确保浏览器被关闭
        scraper._close_browser()

async def main():
    """主测试函数"""
    print("🚀 武汉爬虫进度显示修复测试")
    print("=" * 80)
    
    success = await test_progress_fix()
    
    if success:
        print(f"\n✅ 所有测试通过！进度显示问题已修复。")
    else:
        print(f"\n❌ 测试失败，需要进一步调试。")

if __name__ == "__main__":
    asyncio.run(main())
