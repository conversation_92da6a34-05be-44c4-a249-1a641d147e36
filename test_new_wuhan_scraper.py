#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
测试新版武汉爬虫
"""

import sys
import os
import asyncio
import time

# 添加项目根目录到路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from scrapers.whzbtb_hybrid_scraper_new import WhzbtbHybridScraperNew
from core.db_manager import DatabaseManager

class TestProgressCallback:
    """测试进度回调"""
    
    def __init__(self):
        self.messages = []
        self.progress_values = []
        self.start_time = time.time()
    
    def __call__(self, message, progress):
        """进度回调函数"""
        elapsed = time.time() - self.start_time
        print(f"[{elapsed:6.1f}s] 📊 {progress:5.1f}% - {message}")
        
        self.messages.append(message)
        self.progress_values.append(progress)
        
        # 检查是否包含页数信息
        if "第" in message and "/" in message and ("页" in message or "批" in message):
            try:
                if "批" in message:
                    # 解析批次信息 "解析第1/3批 (1000条)"
                    batch_info = message.split("第")[1].split("批")[0]
                    current_batch = int(batch_info.split("/")[0])
                    total_batches = int(batch_info.split("/")[1])
                    print(f"  ✅ 解析批次: {current_batch}/{total_batches}")
                else:
                    # 解析页数信息 "第1/41页 (1000条)"
                    page_info = message.split("第")[1].split("页")[0]
                    current_page = int(page_info.split("/")[0])
                    total_pages = int(page_info.split("/")[1])
                    print(f"  ✅ 解析页数: {current_page}/{total_pages}")
            except Exception as e:
                print(f"  ❌ 信息解析失败: {e}")
    
    def get_summary(self):
        """获取回调总结"""
        return {
            'total_callbacks': len(self.messages),
            'max_progress': max(self.progress_values) if self.progress_values else 0,
            'final_progress': self.progress_values[-1] if self.progress_values else 0,
            'last_message': self.messages[-1] if self.messages else "无消息",
            'total_time': time.time() - self.start_time
        }

async def test_new_scraper():
    """测试新版武汉爬虫"""
    print("🚀 测试新版武汉爬虫")
    print("=" * 80)
    
    # 创建进度回调
    progress_callback = TestProgressCallback()
    
    # 创建新版爬虫实例
    scraper = WhzbtbHybridScraperNew(headless=True, progress_callback=progress_callback)
    
    try:
        print("1️⃣ 测试招标数据爬取...")
        print("-" * 50)
        
        # 创建数据库管理器
        db = DatabaseManager()
        
        # 测试招标数据爬取
        tender_items = []
        count = 0
        
        start_time = time.time()
        async for item in scraper.scrape_list_async(db, '招标'):
            tender_items.append(item)
            count += 1
            if count >= 50:  # 获取50条数据测试
                break
        
        elapsed_time = time.time() - start_time
        
        # 获取进度回调总结
        summary = progress_callback.get_summary()
        
        print(f"\n📋 招标数据测试结果:")
        print(f"  获取数据: {len(tender_items)} 条")
        print(f"  耗时: {elapsed_time:.1f} 秒")
        print(f"  进度回调次数: {summary['total_callbacks']}")
        print(f"  最大进度值: {summary['max_progress']:.1f}%")
        print(f"  最终进度值: {summary['final_progress']:.1f}%")
        print(f"  最后消息: {summary['last_message']}")
        
        # 显示样本数据
        if tender_items:
            sample = tender_items[0]
            print(f"\n📄 样本数据:")
            print(f"  标题: {sample.title[:50]}...")
            print(f"  价格: {sample.price_text}")
            print(f"  日期: {sample.publish_date}")
            print(f"  编号: {sample.tender_reference}")
            print(f"  URL: {sample.url}")
        
        db.close()
        
        print(f"\n2️⃣ 测试中标数据爬取...")
        print("-" * 50)
        
        # 重置回调记录
        progress_callback.messages.clear()
        progress_callback.progress_values.clear()
        progress_callback.start_time = time.time()
        
        # 测试中标数据
        db = DatabaseManager()
        winbid_items = []
        count = 0
        
        start_time = time.time()
        async for item in scraper.scrape_list_async(db, '中标'):
            winbid_items.append(item)
            count += 1
            if count >= 50:  # 获取50条数据测试
                break
        
        elapsed_time = time.time() - start_time
        
        # 获取进度回调总结
        summary = progress_callback.get_summary()
        
        print(f"\n📋 中标数据测试结果:")
        print(f"  获取数据: {len(winbid_items)} 条")
        print(f"  耗时: {elapsed_time:.1f} 秒")
        print(f"  进度回调次数: {summary['total_callbacks']}")
        print(f"  最大进度值: {summary['max_progress']:.1f}%")
        print(f"  最终进度值: {summary['final_progress']:.1f}%")
        print(f"  最后消息: {summary['last_message']}")
        
        # 显示样本数据
        if winbid_items:
            sample = winbid_items[0]
            print(f"\n📄 样本数据:")
            print(f"  标题: {sample.title[:50]}...")
            print(f"  价格: {sample.price_text}")
            print(f"  日期: {sample.publish_date}")
            print(f"  编号: {sample.tender_reference}")
            print(f"  URL: {sample.url}")
        
        db.close()
        
        # 总结
        print(f"\n" + "=" * 80)
        print("📋 新版武汉爬虫测试总结:")
        print("=" * 80)
        
        overall_success = len(tender_items) > 0 and len(winbid_items) > 0
        
        print(f"招标数据获取: {'✅ 成功' if len(tender_items) > 0 else '❌ 失败'} ({len(tender_items)} 条)")
        print(f"中标数据获取: {'✅ 成功' if len(winbid_items) > 0 else '❌ 失败'} ({len(winbid_items)} 条)")
        print(f"进度回调功能: {'✅ 正常' if summary['total_callbacks'] > 0 else '❌ 异常'}")
        print(f"数据解析功能: {'✅ 正常' if overall_success else '❌ 异常'}")
        
        if overall_success:
            print(f"\n🎉 新版武汉爬虫测试成功！")
            print(f"💡 新版特性:")
            print(f"  - ✅ 一次性获取所有数据，避免重复请求")
            print(f"  - ✅ 分批解析和存储，每批1000条")
            print(f"  - ✅ 实时进度更新，显示解析进度")
            print(f"  - ✅ 自动会话管理，无需手动配置")
            print(f"  - ✅ 完整的错误处理和日志记录")
        else:
            print(f"\n⚠️ 部分功能需要进一步调试")
        
        return overall_success
        
    except Exception as e:
        print(f"❌ 测试过程中出现错误: {e}")
        import traceback
        traceback.print_exc()
        return False
    
    finally:
        # 确保浏览器被关闭
        scraper._close_browser()

async def main():
    """主测试函数"""
    print("🚀 新版武汉爬虫测试")
    print("=" * 80)
    
    success = await test_new_scraper()
    
    if success:
        print(f"\n✅ 所有测试通过！新版武汉爬虫可以投入使用。")
        print(f"\n📝 使用说明:")
        print(f"  1. 将 whzbtb_hybrid_scraper_new.py 替换原有的 whzbtb_hybrid_scraper.py")
        print(f"  2. 新版本会一次性获取所有数据，然后分批解析")
        print(f"  3. 进度条会显示实时的解析进度")
        print(f"  4. 每1000条数据为一批，自动存入数据库")
    else:
        print(f"\n❌ 测试失败，需要进一步调试。")

if __name__ == "__main__":
    asyncio.run(main())
