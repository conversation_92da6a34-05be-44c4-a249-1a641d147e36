#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
分析当前的待提取信息判断逻辑和招标/中标信息提取差异
"""

import sys
import os
import json

# 添加项目根目录到路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from core.db_manager import DatabaseManager
from utils.intelligent_extractor import IntelligentExtractor

def analyze_extraction_criteria():
    """分析待提取信息的判断标准"""
    print("🔍 分析待提取信息判断逻辑")
    print("=" * 60)
    
    db = DatabaseManager()
    
    # 1. 分析数据库中的提取状态
    print("📊 数据库提取状态分析:")
    
    # 获取平台统计
    platform_sql = "SELECT platform_id, COUNT(*) FROM bidding_data GROUP BY platform_id"
    db.cursor.execute(platform_sql)
    platforms = db.cursor.fetchall()
    
    for platform_id, total_count in platforms:
        print(f"\n🏢 平台: {platform_id}")
        print(f"  📋 总数据量: {total_count:,} 条")
        
        # 未提取详情的数据
        unextracted_sql = "SELECT COUNT(*) FROM bidding_data WHERE platform_id = ? AND details_extracted = 0"
        db.cursor.execute(unextracted_sql, (platform_id,))
        unextracted_count = db.cursor.fetchone()[0]
        
        # 已提取详情的数据
        extracted_sql = "SELECT COUNT(*) FROM bidding_data WHERE platform_id = ? AND details_extracted = 1"
        db.cursor.execute(extracted_sql, (platform_id,))
        extracted_count = db.cursor.fetchone()[0]
        
        # 有价格候选项的数据（置信度区间外）
        candidates_sql = """
        SELECT COUNT(*) FROM bidding_data 
        WHERE platform_id = ? AND price_candidates IS NOT NULL AND price_candidates != ''
        """
        db.cursor.execute(candidates_sql, (platform_id,))
        candidates_count = db.cursor.fetchone()[0]
        
        # 需要重新处理的数据（置信度区间外）
        reprocess_sql = """
        SELECT COUNT(*) FROM bidding_data 
        WHERE platform_id = ? AND details_extracted = 1 AND price_candidates IS NOT NULL AND price_candidates != ''
        """
        db.cursor.execute(reprocess_sql, (platform_id,))
        reprocess_count = db.cursor.fetchone()[0]
        
        print(f"  ❌ 未提取详情: {unextracted_count:,} 条 ({unextracted_count/total_count*100:.1f}%)")
        print(f"  ✅ 已提取详情: {extracted_count:,} 条 ({extracted_count/total_count*100:.1f}%)")
        print(f"  🎯 有价格候选项: {candidates_count:,} 条")
        print(f"  🔄 需重新处理: {reprocess_count:,} 条")
        
        # 总的待处理数据
        total_to_process = unextracted_count + reprocess_count
        print(f"  📝 总待处理: {total_to_process:,} 条")
    
    db.close()

def analyze_announcement_type_differences():
    """分析招标和中标信息提取的差异"""
    print("\n🔍 招标vs中标信息提取差异分析")
    print("=" * 60)
    
    # 1. 分析默认提取字段配置
    extractor = IntelligentExtractor()
    default_fields = extractor.DEFAULT_TARGET_FIELDS
    
    print("📋 默认提取字段配置:")
    for field_name, keywords in default_fields.items():
        print(f"\n🏷️  {field_name}:")
        print(f"   关键词: {', '.join(keywords[:5])}{'...' if len(keywords) > 5 else ''}")
        print(f"   总数: {len(keywords)} 个关键词")
    
    # 2. 分析价格关键词的倾向性
    print(f"\n💰 价格关键词分析:")
    price_keywords = default_fields['price']
    
    zhongbiao_keywords = [kw for kw in price_keywords if '中标' in kw or '成交' in kw]
    zhaobiao_keywords = [kw for kw in price_keywords if '投标' in kw or '报价' in kw]
    neutral_keywords = [kw for kw in price_keywords if kw not in zhongbiao_keywords and kw not in zhaobiao_keywords]
    
    print(f"  🎯 中标倾向关键词: {zhongbiao_keywords}")
    print(f"  📝 招标倾向关键词: {zhaobiao_keywords}")
    print(f"  ⚖️  中性关键词: {neutral_keywords}")
    
    # 3. 分析编号字段的通用性
    print(f"\n🔢 编号字段分析:")
    for field_name in ['tender_reference', 'publicity_number', 'registration_id', 'section_number']:
        keywords = default_fields[field_name]
        print(f"  📋 {field_name}: {len(keywords)} 个关键词")
        print(f"     示例: {', '.join(keywords[:3])}")

def analyze_extraction_process():
    """分析提取过程的逻辑"""
    print("\n🔍 提取过程逻辑分析")
    print("=" * 60)
    
    print("📋 提取策略层次:")
    print("  1️⃣  改进的价格提取（置信度系统）")
    print("     - 多候选价格识别")
    print("     - 置信度评分排序")
    print("     - JSON格式存储候选项")
    
    print("  2️⃣  表格识别策略")
    print("     - 结构化表格数据提取")
    print("     - 键值对匹配")
    
    print("  3️⃣  纵向表格百分比识别")
    print("     - 价格后备方案")
    print("     - 百分比格式处理")
    
    print("  4️⃣  文本模式匹配策略")
    print("     - 正则表达式匹配")
    print("     - 关键词：值 模式")
    
    print("  5️⃣  上下文关联策略")
    print("     - 基于关键词的上下文分析")
    print("     - 最后的后备方案")

def analyze_database_criteria():
    """分析数据库中的待提取判断标准"""
    print("\n🔍 数据库待提取判断标准")
    print("=" * 60)
    
    print("📋 待提取数据的判断逻辑:")
    print("  ❌ 未提取详情数据:")
    print("     条件: details_extracted = 0")
    print("     说明: 从未进行过详情页提取的数据")
    
    print("  🔄 置信度区间外数据:")
    print("     条件: details_extracted = 1 AND price_candidates IS NOT NULL AND price_candidates != ''")
    print("     说明: 已提取但价格置信度不在可接受范围内的数据")
    
    print("  📝 优先级处理:")
    print("     1. 优先处理未提取详情的数据 (details_extracted = 0)")
    print("     2. 然后处理置信度区间外的数据")
    print("     3. 按发布日期倒序排列 (publish_date DESC)")

def check_announcement_type_usage():
    """检查announcement_type参数的使用情况"""
    print("\n🔍 announcement_type参数使用分析")
    print("=" * 60)
    
    db = DatabaseManager()
    
    # 检查数据库中的公告类型分布
    type_sql = "SELECT announcement_type, COUNT(*) FROM bidding_data GROUP BY announcement_type"
    db.cursor.execute(type_sql)
    types = db.cursor.fetchall()
    
    print("📊 数据库中的公告类型分布:")
    for announcement_type, count in types:
        print(f"  📋 {announcement_type}: {count:,} 条")
    
    # 检查是否有针对不同公告类型的特殊处理
    print("\n🔍 提取器中的公告类型处理:")
    print("  📝 IntelligentExtractor.extract_information()方法:")
    print("     - 接收announcement_type参数")
    print("     - 但当前实现中未针对不同类型做特殊处理")
    print("     - 使用相同的DEFAULT_TARGET_FIELDS配置")
    
    print("  💡 潜在优化点:")
    print("     - 招标公告可能更关注'预算金额'、'最高限价'等")
    print("     - 中标公告更关注'中标价'、'成交价'等")
    print("     - 可以根据announcement_type动态调整关键词权重")
    
    db.close()

def main():
    """主分析函数"""
    print("🚀 待提取信息判断逻辑和提取差异分析")
    print("=" * 80)
    
    try:
        # 1. 分析提取标准
        analyze_extraction_criteria()
        
        # 2. 分析公告类型差异
        analyze_announcement_type_differences()
        
        # 3. 分析提取过程
        analyze_extraction_process()
        
        # 4. 分析数据库判断标准
        analyze_database_criteria()
        
        # 5. 检查公告类型使用情况
        check_announcement_type_usage()
        
        # 总结
        print("\n" + "=" * 80)
        print("📋 分析总结:")
        print("=" * 80)
        
        print("🎯 待提取信息判断逻辑:")
        print("  1. details_extracted = 0 (未提取详情)")
        print("  2. details_extracted = 1 AND price_candidates != '' (置信度区间外)")
        print("  3. 优先级: 未提取 > 置信度区间外")
        
        print("\n🔍 招标vs中标提取差异:")
        print("  ❌ 当前状态: 使用相同的提取逻辑和关键词")
        print("  💡 优化建议:")
        print("     - 招标公告: 关注预算金额、最高限价、投标截止时间")
        print("     - 中标公告: 关注中标价、成交价、中标单位")
        print("     - 可根据announcement_type动态调整关键词权重")
        
        print("\n✅ 系统特点:")
        print("  🧠 智能化: 多层次递进式提取策略")
        print("  🎯 精确性: 置信度评分系统")
        print("  🔄 完整性: 支持重新处理低置信度数据")
        print("  📊 可扩展: 支持平台特定优化")
        
    except Exception as e:
        print(f"❌ 分析过程中发生错误: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
