#!/usr/bin/env python3
"""
分析当前数据库中招投标公告关联规则
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from core.db_manager import DatabaseManager
from utils.project_matcher import ProjectMatcher
from models.data_model import BiddingData

def analyze_association_rules():
    """分析当前的招投标公告关联规则"""
    print("🔍 分析当前数据库中的招投标公告关联规则...")
    
    db = DatabaseManager()
    
    try:
        # 1. 统计基本数据
        print("\n📊 基本数据统计:")
        
        # 总数据量
        db.cursor.execute("SELECT COUNT(*) FROM bidding_data")
        total_count = db.cursor.fetchone()[0]
        print(f"总数据量: {total_count:,} 条")
        
        # 按公告类型统计
        db.cursor.execute("SELECT announcement_type, COUNT(*) FROM bidding_data GROUP BY announcement_type")
        type_stats = db.cursor.fetchall()
        for ann_type, count in type_stats:
            print(f"  {ann_type}: {count:,} 条")
        
        # 2. 分析关联字段的填充情况
        print("\n📋 关联字段填充情况:")
        
        association_fields = [
            ('tender_reference', '招标编号'),
            ('publicity_number', '公示编号'),
            ('registration_id', '招标登记编号'),
            ('section_number', '标段编号')
        ]
        
        for field, description in association_fields:
            # 统计非空字段
            db.cursor.execute(f"SELECT COUNT(*) FROM bidding_data WHERE {field} IS NOT NULL AND {field} != ''")
            filled_count = db.cursor.fetchone()[0]
            fill_rate = (filled_count / total_count * 100) if total_count > 0 else 0
            print(f"  {description} ({field}): {filled_count:,} 条 ({fill_rate:.1f}%)")
            
            # 按公告类型分别统计
            for ann_type, _ in type_stats:
                db.cursor.execute(f"""
                SELECT COUNT(*) FROM bidding_data 
                WHERE {field} IS NOT NULL AND {field} != '' AND announcement_type = ?
                """, (ann_type,))
                type_filled = db.cursor.fetchone()[0]
                
                db.cursor.execute("SELECT COUNT(*) FROM bidding_data WHERE announcement_type = ?", (ann_type,))
                type_total = db.cursor.fetchone()[0]
                
                type_rate = (type_filled / type_total * 100) if type_total > 0 else 0
                print(f"    {ann_type}: {type_filled:,} 条 ({type_rate:.1f}%)")
        
        # 3. 分析现有的关联规则
        print("\n🔗 现有关联规则分析:")
        
        # 检查相同编号的数据
        print("基于编号字段的潜在关联:")
        
        for field, description in association_fields:
            # 查找有相同编号的不同公告类型
            query_sql = f"""
            SELECT {field}, COUNT(*) as count, 
                   GROUP_CONCAT(DISTINCT announcement_type) as types
            FROM bidding_data 
            WHERE {field} IS NOT NULL AND {field} != ''
            GROUP BY {field}
            HAVING COUNT(*) > 1 AND COUNT(DISTINCT announcement_type) > 1
            LIMIT 10
            """
            
            db.cursor.execute(query_sql)
            cross_type_matches = db.cursor.fetchall()
            
            if cross_type_matches:
                print(f"\n  {description}字段的跨类型匹配示例:")
                for ref_num, count, types in cross_type_matches[:5]:
                    print(f"    编号: {ref_num} -> {count}条记录 (类型: {types})")
            else:
                print(f"  {description}: 无跨类型匹配")
        
        # 4. 分析标题相似度匹配
        print("\n📝 标题相似度匹配分析:")
        
        # 获取少量样本数据进行分析
        sample_sql = """
        SELECT * FROM bidding_data 
        WHERE announcement_type IN ('招标', '中标')
        ORDER BY scraped_timestamp DESC 
        LIMIT 100
        """
        
        db.cursor.execute(sample_sql)
        sample_rows = db.cursor.fetchall()
        
        if sample_rows:
            # 转换为BiddingData对象
            column_names = [description[0] for description in db.cursor.description]
            sample_data = []
            
            for row in sample_rows:
                row_dict = dict(zip(column_names, row))
                # 设置db_id
                row_dict['db_id'] = row_dict.get('db_id')
                sample_data.append(BiddingData(**row_dict))
            
            # 使用ProjectMatcher进行匹配分析
            matches = ProjectMatcher.find_project_matches(sample_data)
            
            # 统计匹配结果
            match_stats = {'none': 0, 'high_unique': 0, 'low_or_multiple': 0}
            for match_info in matches.values():
                match_stats[match_info['match_status']] += 1
            
            total_matches = len(matches)
            print(f"样本匹配分析 (基于{total_matches}个中标项目):")
            for status, count in match_stats.items():
                rate = (count / total_matches * 100) if total_matches > 0 else 0
                status_desc = {
                    'none': '无匹配',
                    'high_unique': '高质量唯一匹配',
                    'low_or_multiple': '低质量或多重匹配'
                }[status]
                print(f"  {status_desc}: {count} 项 ({rate:.1f}%)")
            
            # 展示一些匹配示例
            print("\n匹配示例:")
            example_count = 0
            for item_key, match_info in matches.items():
                if example_count >= 3:
                    break
                
                current_item = match_info['current_item']
                tender_matches = match_info['tender_matches']
                match_status = match_info['match_status']
                
                print(f"\n  中标项目: {current_item.title[:50]}...")
                print(f"  匹配状态: {match_status}")
                
                if tender_matches:
                    best_match = tender_matches[0]  # 最高相似度的匹配
                    print(f"  最佳匹配: {best_match[0].title[:50]}...")
                    print(f"  相似度: {best_match[1]:.3f}")
                
                example_count += 1
        
        # 5. 总结当前关联规则
        print("\n📋 当前关联规则总结:")
        print("=" * 60)
        
        print("1. 基于编号字段的精确匹配:")
        print("   - tender_reference (招标编号)")
        print("   - publicity_number (公示编号)")  
        print("   - registration_id (招标登记编号)")
        print("   - section_number (标段编号)")
        
        print("\n2. 基于标题相似度的模糊匹配:")
        print("   - 使用最长公共子序列算法")
        print("   - 相似度阈值: 0.8 (高质量匹配)")
        print("   - 支持标题清理和标准化")
        
        print("\n3. 匹配策略:")
        print("   - 同平台内匹配 (platform_id相同)")
        print("   - 中标项目寻找对应的招标项目")
        print("   - 优先级: 编号匹配 > 标题相似度匹配")
        
        print("\n4. 匹配质量分类:")
        print("   - high_unique: 高相似度(≥0.8)的唯一匹配")
        print("   - low_or_multiple: 低相似度或多重匹配")
        print("   - none: 无匹配")
        
    except Exception as e:
        print(f"❌ 分析过程中发生错误: {e}")
        import traceback
        traceback.print_exc()
    
    finally:
        db.close()

if __name__ == "__main__":
    analyze_association_rules()
