#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
武汉市公共资源交易平台爬虫 - 新版本
优化后的一次性获取，分批解析版本
"""

import asyncio
import json
import random
import time
from typing import AsyncGenerator, Dict, Any, List, Optional

import requests
from selenium import webdriver
from selenium.webdriver.edge.options import Options as EdgeOptions
from selenium.webdriver.chrome.options import Options as ChromeOptions

from .base_scraper import BaseScraper
from core.db_manager import DatabaseManager
from models.data_model import BiddingData


class WhzbtbHybridScraperNew(BaseScraper):
    """武汉市公共资源交易平台爬虫 - 新版本"""

    def __init__(self, headless=True, progress_callback=None):
        super().__init__()
        self.platform_id = 'whzbtb'
        self.platform_name = '武汉市公共资源交易平台'
        self.base_url = 'https://www.whzbtb.com'
        self.headless = headless
        self.progress_callback = progress_callback
        
        # API端点
        self.api_endpoints = {
            '中标': 'https://www.whzbtb.com/V2PRTS/WinBidBulletinInfoList.do',
            '招标': 'https://www.whzbtb.com/V2PRTS/TendererNoticeInfoList.do'
        }
        
        # 会话管理
        self.session = requests.Session()
        self.session.headers.update({
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36 Edg/120.0.0.0'
        })
        self.driver = None
        self.session_initialized = {'中标': False, '招标': False}

        # 会话缓存 - 一次获取，多次使用
        self.session_cookies = {'中标': None, '招标': None}  # 缓存会话Cookie
        self.session_headers = {'中标': None, '招标': None}  # 缓存会话Headers
        self.session_expiry = {'中标': None, '招标': None}  # 会话过期时间
        self.session_cache_duration = 1800  # 会话缓存30分钟

    def __del__(self):
        """析构函数，确保浏览器被关闭"""
        self._close_browser()

    def _close_browser(self):
        """关闭浏览器"""
        if self.driver:
            try:
                self.driver.quit()
                self._log_message("✅ 浏览器已关闭")
            except:
                pass
            finally:
                self.driver = None

    def _log_message(self, message: str):
        """统一的日志输出方法"""
        print(message)
        if self.progress_callback:
            try:
                self.progress_callback(message, 0)
            except:
                pass

    def _report_progress(self, message: str, progress: float):
        """报告进度"""
        self._log_message(message)
        if self.progress_callback:
            try:
                # 确保进度信息格式正确，便于GUI解析
                formatted_message = message
                if "第" not in message and "页" not in message and progress < 100:
                    if "批次" in message or "/" in message:
                        formatted_message = message
                    else:
                        current_batch = int(progress / 100 * 10) + 1 if progress > 0 else 1
                        formatted_message = f"第{current_batch}/10页 - {message}"
                
                self.progress_callback(formatted_message, progress)
            except:
                pass

    def _setup_browser(self):
        """设置无头浏览器，优先Edge，失败后尝试Chrome"""
        common_args = [
            '--headless',
            '--no-sandbox',
            '--disable-dev-shm-usage',
            '--disable-gpu',
            '--disable-web-security',
            '--disable-features=VizDisplayCompositor',
            '--window-size=1920,1080'
        ]
        
        # 首先尝试Edge
        try:
            options = EdgeOptions()
            for arg in common_args:
                options.add_argument(arg)
            self.driver = webdriver.Edge(options=options)
            self._log_message("✅ Edge浏览器启动成功")
            return
        except Exception as e:
            self._log_message(f"⚠️ Edge浏览器启动失败: {e}")
            
        # Edge失败后尝试Chrome
        try:
            options = ChromeOptions()
            for arg in common_args:
                options.add_argument(arg)
            self.driver = webdriver.Chrome(options=options)
            self._log_message("✅ Chrome浏览器启动成功")
            return
        except Exception as e:
            self._log_message(f"⚠️ Chrome浏览器启动失败: {e}")
            raise Exception("无法启动任何浏览器")

    def _setup_visible_browser(self):
        """设置非无头浏览器（用于绕过反爬措施）"""
        if self.driver is not None:
            return

        # 非无头浏览器参数
        common_args = [
            '--no-sandbox',
            '--disable-dev-shm-usage',
            '--disable-blink-features=AutomationControlled',
            '--window-size=1200,800'
        ]

        # 首先尝试Edge（非无头模式）
        try:
            self._log_message(f"🌐 启动非无头Edge浏览器...")
            options = EdgeOptions()
            for arg in common_args:
                options.add_argument(arg)
            options.add_experimental_option("excludeSwitches", ["enable-automation"])
            options.add_experimental_option('useAutomationExtension', False)
            options.add_argument('--user-agent=Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36 Edg/120.0.0.0')

            self.driver = webdriver.Edge(options=options)

            # 隐藏自动化特征
            self.driver.execute_script("Object.defineProperty(navigator, 'webdriver', {get: () => undefined})")

            self._log_message(f"✅ 非无头Edge浏览器启动成功")
            return

        except Exception as e:
            self._log_message(f"⚠️ 非无头Edge浏览器启动失败: {e}")

        # Edge失败后尝试Chrome（非无头模式）
        try:
            self._log_message(f"🌐 启动非无头Chrome浏览器...")
            options = ChromeOptions()
            for arg in common_args:
                options.add_argument(arg)
            options.add_experimental_option("excludeSwitches", ["enable-automation"])
            options.add_experimental_option('useAutomationExtension', False)
            options.add_argument('--user-agent=Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36')

            self.driver = webdriver.Chrome(options=options)

            # 隐藏自动化特征
            self.driver.execute_script("Object.defineProperty(navigator, 'webdriver', {get: () => undefined})")

            self._log_message(f"✅ 非无头Chrome浏览器启动成功")
            return

        except Exception as e:
            self._log_message(f"⚠️ 非无头Chrome浏览器启动失败: {e}")
            raise Exception("无法启动任何非无头浏览器")

    def _initialize_session_with_browser(self, announcement_type: str) -> bool:
        """使用无头浏览器自动获取会话信息"""
        try:
            self._setup_browser()

            # 根据公告类型访问对应的首页
            if announcement_type == '中标':
                home_url = "https://www.whzbtb.com/V2PRTS/WinBidBulletinInfoListInit.do"
            else:
                home_url = "https://www.whzbtb.com/V2PRTS/TendererNoticeInfoListInit.do"

            self._log_message(f"🌐 访问{announcement_type}首页: {home_url}")
            self.driver.get(home_url)

            # 等待页面加载完成
            time.sleep(5)

            # 特别处理中标页面，需要更多交互来获取有效会话
            if announcement_type == '中标':
                try:
                    # 1. 先访问主页建立基础会话
                    self._log_message(f"🔄 访问主页建立基础会话...")
                    self.driver.get("https://www.whzbtb.com/")
                    time.sleep(3)

                    # 2. 再访问中标列表初始化页面
                    self._log_message(f"🔄 重新访问中标初始化页面...")
                    self.driver.get(home_url)
                    time.sleep(5)

                    # 3. 执行页面交互，模拟真实用户行为
                    self.driver.execute_script("window.scrollTo(0, 100);")
                    time.sleep(2)

                    # 4. 尝试触发页面的Ajax请求来建立完整会话
                    self.driver.execute_script("""
                        // 尝试触发页面的初始化函数
                        if (typeof initPage === 'function') initPage();
                        if (typeof loadData === 'function') loadData();
                        if (typeof queryData === 'function') queryData();

                        // 模拟表单提交来建立会话
                        var forms = document.getElementsByTagName('form');
                        if (forms.length > 0) {
                            console.log('Found forms:', forms.length);
                        }
                    """)
                    time.sleep(3)

                    # 5. 尝试模拟一次实际的API调用来激活会话
                    self._log_message(f"🔄 尝试激活中标会话...")
                    self.driver.execute_script("""
                        // 尝试发送一个测试请求来激活会话
                        if (typeof jQuery !== 'undefined' || typeof $ !== 'undefined') {
                            var $ = jQuery || $;
                            $.post('/V2PRTS/WinBidBulletinInfoList.do', {
                                page: 1,
                                rows: 1
                            }).done(function(data) {
                                console.log('Session activation successful');
                            }).fail(function() {
                                console.log('Session activation failed');
                            });
                        }
                    """)
                    time.sleep(3)

                except Exception as interaction_error:
                    self._log_message(f"⚠️ 中标页面特殊交互失败: {interaction_error}")
            else:
                # 招标页面的标准交互
                try:
                    self.driver.execute_script("window.scrollTo(0, 100);")
                    time.sleep(2)
                except Exception as interaction_error:
                    self._log_message(f"⚠️ 页面交互失败: {interaction_error}")

            # 获取所有Cookie并转移到requests session
            cookies = self.driver.get_cookies()
            cookie_count = 0
            for cookie in cookies:
                self.session.cookies.set(cookie['name'], cookie['value'])
                cookie_count += 1
                self._log_message(f"  获取Cookie: {cookie['name']} = {cookie['value'][:20]}...")

            # 设置额外的请求头，模拟真实浏览器
            self.session.headers.update({
                'Referer': home_url,
                'Origin': 'https://www.whzbtb.com',
                'Accept': 'application/json, text/javascript, */*; q=0.01',
                'Accept-Language': 'zh-CN,zh;q=0.9,en;q=0.8',
                'X-Requested-With': 'XMLHttpRequest',
                'Content-Type': 'application/x-www-form-urlencoded; charset=UTF-8'
            })

            self._log_message(f"✅ {announcement_type}浏览器会话获取成功，Cookie数量: {cookie_count}")
            self._close_browser()
            return True

        except Exception as e:
            self._log_message(f"❌ {announcement_type}浏览器会话获取失败: {e}")
            self._close_browser()
            return False

    def _initialize_session(self, announcement_type: str) -> bool:
        """初始化会话"""
        if self.session_initialized.get(announcement_type, False):
            return True

        # 特殊处理：中标数据使用招标页面的会话
        if announcement_type == '中标':
            self._log_message(f"🔄 中标数据使用招标会话（绕过安全限制）...")

            # 先确保招标会话已初始化
            if not self.session_initialized.get('招标', False):
                self._log_message(f"🌐 先初始化招标会话...")
                if not self._initialize_session_with_browser('招标'):
                    self._log_message(f"❌ 招标会话初始化失败")
                    return False
                self.session_initialized['招标'] = True

            # 使用招标会话的Cookie和请求头来访问中标API
            self._log_message(f"✅ 使用招标会话访问中标API")
            self.session_initialized['中标'] = True
            return True
        else:
            # 招标数据的正常初始化流程
            self._log_message(f"🌐 使用浏览器获取{announcement_type}会话信息...")

            # 多次尝试浏览器获取会话
            max_retries = 3
            for attempt in range(max_retries):
                try:
                    if self._initialize_session_with_browser(announcement_type):
                        self.session_initialized[announcement_type] = True
                        return True
                    else:
                        if attempt < max_retries - 1:
                            self._log_message(f"⚠️ 第{attempt + 1}次尝试失败，等待后重试...")
                            time.sleep(2)
                        else:
                            self._log_message(f"❌ 浏览器获取失败，已尝试{max_retries}次")
                except Exception as e:
                    self._log_message(f"❌ 第{attempt + 1}次尝试异常: {e}")
                    if attempt < max_retries - 1:
                        time.sleep(2)

            # 浏览器失败后，回退到简单requests方式
            self._log_message(f"⚠️ {announcement_type}浏览器会话获取失败，使用简单会话")

            # 设置基本的请求头
            referer_url = "https://www.whzbtb.com/V2PRTS/TendererNoticeInfoListInit.do"

            self.session.headers.update({
                'Referer': referer_url,
                'Origin': 'https://www.whzbtb.com',
                'Accept': 'application/json, text/javascript, */*; q=0.01',
                'Accept-Language': 'zh-CN,zh;q=0.9,en;q=0.8',
                'X-Requested-With': 'XMLHttpRequest',
                'Content-Type': 'application/x-www-form-urlencoded; charset=UTF-8'
            })

            self.session_initialized[announcement_type] = True
            return True

    async def _fetch_data_batch(self, page: int, rows: int, announcement_type: str) -> Dict[str, Any]:
        """获取一批数据（默认30秒超时）"""
        return await self._fetch_data_batch_with_timeout(page, rows, announcement_type, 30)

    async def _fetch_data_batch_with_timeout(self, page: int, rows: int, announcement_type: str, timeout: int) -> Dict[str, Any]:
        """获取一批数据（自定义超时）"""
        if not self._initialize_session(announcement_type):
            return None

        api_url = self.api_endpoints[announcement_type]

        payload = {
            'page': page,
            'rows': rows
        }

        # 对于中标数据，使用非无头浏览器绕过反爬措施
        if announcement_type == '中标':
            return await self._fetch_data_with_visible_browser(page, rows, announcement_type, timeout)

        try:
            self._log_message(f"正在请求API: page={page}, rows={rows}, timeout={timeout}s")
            self._log_message(f"🔍 API URL: {api_url}")
            self._log_message(f"🔍 请求参数: {payload}")

            response = await asyncio.to_thread(
                self.session.post,
                api_url,
                data=payload,
                timeout=timeout
            )

            # 详细调试信息
            self._log_message(f"🔍 响应状态码: {response.status_code}")
            self._log_message(f"🔍 响应头: {dict(response.headers)}")

            response_text = response.text
            self._log_message(f"🔍 响应内容长度: {len(response_text)}")
            self._log_message(f"🔍 响应内容前500字符: {response_text[:500]}")

            if response.status_code == 200:
                # 检查响应是否为空
                if not response_text.strip():
                    self._log_message(f"❌ 服务器返回空响应")
                    return None

                # 检查是否为HTML错误页面
                if response_text.strip().startswith('<'):
                    self._log_message(f"❌ 服务器返回HTML页面而非JSON数据")
                    return None

                try:
                    data = response.json()
                    if 'rows' in data:
                        self._log_message(f"API请求成功，返回 {len(data['rows'])} 条数据")
                        return data
                    else:
                        self._log_message(f"❌ 响应中没有'rows'字段: {list(data.keys())}")
                        return None
                except ValueError as json_error:
                    self._log_message(f"❌ JSON解析失败: {json_error}")
                    return None
            else:
                self._log_message(f"❌ API请求失败，状态码: {response.status_code}")
                self._log_message(f"❌ 错误响应内容: {response_text[:1000]}")
                return None

        except Exception as e:
            self._log_message(f"❌ 获取数据失败: {e}")
            import traceback
            self._log_message(f"❌ 详细错误信息: {traceback.format_exc()}")
            return None

    def _is_session_valid(self, announcement_type: str) -> bool:
        """检查会话是否有效"""
        import time

        if not self.session_cookies.get(announcement_type):
            return False

        expiry = self.session_expiry.get(announcement_type)
        if not expiry or time.time() > expiry:
            return False

        return True

    def _cache_session(self, announcement_type: str, cookies, headers):
        """缓存会话信息"""
        import time

        self.session_cookies[announcement_type] = cookies
        self.session_headers[announcement_type] = headers
        self.session_expiry[announcement_type] = time.time() + self.session_cache_duration

        self._log_message(f"✅ {announcement_type}会话信息已缓存，有效期30分钟")

    def _apply_cached_session(self, announcement_type: str):
        """应用缓存的会话信息"""
        cookies = self.session_cookies[announcement_type]
        headers = self.session_headers[announcement_type]

        # 应用cookies
        self.session.cookies.clear()
        for cookie in cookies:
            self.session.cookies.set(cookie['name'], cookie['value'])

        # 应用headers
        self.session.headers.update(headers)

        self._log_message(f"✅ 使用缓存的{announcement_type}会话信息")

    async def _fetch_data_with_visible_browser(self, page: int, rows: int, announcement_type: str, timeout: int) -> Dict[str, Any]:
        """使用非无头浏览器绕过反爬措施，然后用requests获取数据"""

        # 检查是否有有效的缓存会话
        if self._is_session_valid(announcement_type):
            self._log_message(f"🔄 使用缓存的{announcement_type}会话信息")
            self._apply_cached_session(announcement_type)
            # 直接进行API调用，跳过浏览器启动
            return await self._call_api_with_session(page, rows, announcement_type, timeout)

        try:
            self._log_message(f"🌐 获取新的{announcement_type}会话信息...")

            # 启动非无头浏览器
            self._setup_visible_browser()

            # 访问对应的初始化页面
            if announcement_type == '中标':
                init_url = "https://www.whzbtb.com/V2PRTS/WinBidBulletinInfoListInit.do"
            else:
                init_url = "https://www.whzbtb.com/V2PRTS/TendererNoticeInfoListInit.do"

            self._log_message(f"🌐 访问{announcement_type}初始化页面...")
            self.driver.get(init_url)

            # 等待页面完全加载
            time.sleep(5)

            # 模拟用户行为，增加真实性
            self._log_message(f"🤖 模拟用户行为...")
            self.driver.execute_script("window.scrollTo(0, 100);")
            time.sleep(2)
            self.driver.execute_script("window.scrollTo(0, 0);")
            time.sleep(1)

            # 获取所有Cookie
            cookies = self.driver.get_cookies()
            cookie_count = len(cookies)
            self._log_message(f"✅ 获取到 {cookie_count} 个Cookie")

            # 设置完整的请求头，模拟真实浏览器
            headers = {
                'Referer': init_url,
                'Origin': 'https://www.whzbtb.com',
                'Accept': 'application/json, text/javascript, */*; q=0.01',
                'Accept-Language': 'zh-CN,zh;q=0.9,en;q=0.8',
                'Accept-Encoding': 'gzip, deflate, br',
                'X-Requested-With': 'XMLHttpRequest',
                'Content-Type': 'application/x-www-form-urlencoded; charset=UTF-8',
                'Cache-Control': 'no-cache',
                'Pragma': 'no-cache'
            }

            # 缓存会话信息
            self._cache_session(announcement_type, cookies, headers)

            # 应用会话信息到当前session
            self._apply_cached_session(announcement_type)

            # 关闭浏览器
            self._close_browser()

            # 现在使用获取到的会话信息进行API调用
            return await self._call_api_with_session(page, rows, announcement_type, timeout)

        except Exception as e:
            self._log_message(f"❌ 非无头浏览器获取会话失败: {e}")
            self._close_browser()
            return None

    async def _call_api_with_session(self, page: int, rows: int, announcement_type: str, timeout: int) -> Dict[str, Any]:
        """使用已获取的会话信息调用API"""
        try:
            # 现在使用获取到的会话信息进行API调用
            api_url = self.api_endpoints[announcement_type]
            payload = {
                'page': page,
                'rows': rows
            }

            self._log_message(f"🔄 使用获取的会话信息调用API...")
            self._log_message(f"🔍 API URL: {api_url}")
            self._log_message(f"🔍 请求参数: {payload}")

            response = await asyncio.to_thread(
                self.session.post,
                api_url,
                data=payload,
                timeout=timeout
            )

            # 详细调试信息
            self._log_message(f"🔍 响应状态码: {response.status_code}")
            self._log_message(f"🔍 响应头: {dict(response.headers)}")

            response_text = response.text
            self._log_message(f"🔍 响应内容长度: {len(response_text)}")
            self._log_message(f"🔍 响应内容前500字符: {response_text[:500]}")

            if response.status_code == 200:
                # 检查响应是否为空
                if not response_text.strip():
                    self._log_message(f"❌ 服务器返回空响应")
                    return None

                # 检查是否为HTML错误页面
                if response_text.strip().startswith('<'):
                    self._log_message(f"❌ 服务器返回HTML页面而非JSON数据")
                    return None

                try:
                    data = response.json()
                    if 'rows' in data:
                        self._log_message(f"✅ 非无头浏览器方法成功！返回 {len(data['rows'])} 条数据")
                        return data
                    else:
                        self._log_message(f"❌ 响应中没有'rows'字段: {list(data.keys())}")
                        return None
                except ValueError as json_error:
                    self._log_message(f"❌ JSON解析失败: {json_error}")
                    return None
            else:
                self._log_message(f"❌ API请求失败，状态码: {response.status_code}")
                self._log_message(f"❌ 错误响应内容: {response_text[:1000]}")
                return None

        except Exception as e:
            self._log_message(f"❌ API调用失败: {e}")
            import traceback
            self._log_message(f"❌ 详细错误信息: {traceback.format_exc()}")
            return None

    def set_progress_callback(self, callback):
        """设置进度回调函数"""
        self.progress_callback = callback



    async def scrape_list_async(self, db: DatabaseManager, announcement_type: str = '中标') -> AsyncGenerator[BiddingData, None]:
        """
        【异步】爬取单一类型的数据 - 一次性获取，分批解析版本。
        这是暴露给外部调用的主要接口。
        """
        self._log_message(f"开始混合爬取{announcement_type}数据...")

        # 第一步：获取总数据量
        self._report_progress("正在获取数据总量...", 5.0)
        basic_data = await self._fetch_data_batch(1, 10, announcement_type)
        if not basic_data or 'total' not in basic_data:
            self._log_message(f"无法获取{announcement_type}基本信息，任务中止。")
            return  # 异步生成器中的return是合法的，表示结束生成

        total_count = int(basic_data['total'])
        self._log_message(f"服务器{announcement_type}总数据量: {total_count}")

        # 第二步：检查数据库已有数量
        existing_count = await asyncio.to_thread(db.count_items, self.platform_id, announcement_type)
        new_data_count = total_count - existing_count

        if new_data_count <= 0:
            self._report_progress("数据库数据已是最新，无需爬取。", 100.0)
            return  # 异步生成器中的return是合法的，表示结束生成

        self._log_message(f"数据库已有: {existing_count}, 计划获取新数据: {new_data_count}")

        # 第三步：分页获取数据
        # 使用正确的分页方式：page=页码, rows=每页条数

        rows_per_page = 2000  # 每页获取2000条数据
        all_rows = []

        # 计算需要获取的页数
        pages_needed = (new_data_count + rows_per_page - 1) // rows_per_page
        self._log_message(f"计划获取 {new_data_count} 条数据，需要 {pages_needed} 页")

        for page_num in range(1, pages_needed + 1):
            # 计算当前页应该获取的数据量
            if page_num == pages_needed:
                # 最后一页，可能不足rows_per_page条
                current_page_rows = new_data_count - (page_num - 1) * rows_per_page
            else:
                current_page_rows = rows_per_page

            progress = 10 + ((page_num - 1) / pages_needed) * 30  # 10%-40%用于数据获取
            self._report_progress(f"获取第{page_num}/{pages_needed}页数据 ({current_page_rows}条)...", progress)

            # 使用正确的分页参数：page=页码, rows=每页条数
            page_data = await self._fetch_data_batch_with_timeout(page_num, rows_per_page, announcement_type, 60)

            if page_data and 'rows' in page_data:
                page_rows = page_data['rows']
                # 只取需要的数据量（最后一页可能返回的数据超过需要的量）
                actual_rows = page_rows[:current_page_rows]
                all_rows.extend(actual_rows)
                self._log_message(f"第{page_num}页获取成功: {len(actual_rows)} 条")

                # 如果返回的数据少于预期，说明已经到了数据末尾
                if len(page_rows) < rows_per_page:
                    self._log_message(f"第{page_num}页返回数据不足，已到数据末尾")
                    break
            else:
                self._log_message(f"第{page_num}页获取失败，跳过")
                # 继续尝试下一页，不中断整个流程

            # 页面间短暂延时，避免请求过于频繁
            await asyncio.sleep(0.5)
        actual_count = len(all_rows)
        self._log_message(f"总共获取到 {actual_count} 条数据")

        # 第四步：分批解析和存储
        parse_batch_size = 1000  # 每批解析1000条
        processed = 0
        total_parse_batches = (actual_count + parse_batch_size - 1) // parse_batch_size

        for batch_num in range(1, total_parse_batches + 1):
            start_idx = (batch_num - 1) * parse_batch_size
            end_idx = min(start_idx + parse_batch_size, actual_count)
            batch_rows = all_rows[start_idx:end_idx]

            # 报告解析进度
            progress_percent = 50 + (batch_num / total_parse_batches) * 45  # 50%-95%用于解析
            self._report_progress(f"解析第{batch_num}/{total_parse_batches}批 ({len(batch_rows)}条)", progress_percent)

            # 解析当前批次的数据
            batch_processed = 0
            for item_json in batch_rows:
                try:
                    item = self._parse_item(item_json, announcement_type)
                    if item:
                        yield item
                        batch_processed += 1
                except Exception as e:
                    self._log_message(f"解析数据项失败: {e}")
                    continue

            # 更新总处理计数
            processed += batch_processed

            # 报告批次完成进度
            progress_percent = 50 + (processed / actual_count) * 45  # 50%-95%
            self._report_progress(f"第{batch_num}/{total_parse_batches}批完成 (已解析{processed}/{actual_count}条)", progress_percent)

            # 批次间短暂延时
            await asyncio.sleep(0.5)

        # 完成进度报告
        self._report_progress(f"爬取完成，共获取 {processed} 条新数据", 100.0)

    def _parse_winning_bid_item(self, item_json: Dict[str, Any]) -> Optional[BiddingData]:
        """专门解析中标数据项（用于批量获取）"""
        try:
            # 中标数据字段映射
            title = item_json.get('prjName', '').strip()
            if not title:
                return None

            # 中标价格 - 使用winBidPrice字段 (万元)
            win_bid_price = item_json.get('winBidPrice')
            if win_bid_price and win_bid_price > 0:
                price_text = f"{win_bid_price}万元"
            else:
                price_text = ""

            # 中标日期 - 使用insertDate字段
            publish_date = item_json.get('insertDate', '')
            if publish_date:
                # 只取日期部分
                publish_date = publish_date.split(' ')[0]

            # 中标编号 - 使用publicityNumber字段
            tender_reference = item_json.get('publicityNumber', '')

            # 构造详情页URL
            item_id = item_json.get('id', '')
            if item_id:
                detail_url = f"https://www.whzbtb.com/V2PRTS/WinBidBulletinInfoDetail.do?id={item_id}"
            else:
                detail_url = ""

            # 创建BiddingData对象
            bidding_data = BiddingData(
                url=detail_url,
                platform_id='whzbtb',
                title=title,
                publish_date=publish_date,
                announcement_type='中标',
                price_text=price_text,
                tender_reference=tender_reference,
                region='武汉市'
            )

            return bidding_data

        except Exception as e:
            self._log_message(f"⚠️ 解析中标数据项失败: {e}")
            return None

    def _parse_item(self, item_json: Dict[str, Any], announcement_type: str) -> BiddingData:
        """解析单个数据项"""
        try:
            # 根据公告类型使用不同的字段映射
            if announcement_type == '中标':
                # 中标数据：优先使用prjName字段
                title = item_json.get('prjName', '').strip()
                if not title:
                    title = item_json.get('tenderPrjName', '').strip()

                # 中标价格 - 使用winBidPrice字段 (万元)
                win_bid_price = item_json.get('winBidPrice')
                if win_bid_price and isinstance(win_bid_price, (int, float)) and win_bid_price > 0:
                    price_text = f"{win_bid_price}万元"
                    price_normalized = float(win_bid_price) * 10000  # 转换为元
                else:
                    price_text = ""
                    price_normalized = 0.0

                # 中标日期 - 使用insertDate字段
                publish_date = item_json.get('insertDate', '')
                if not publish_date:
                    publish_date = item_json.get('noticeStartDate', '')

                # 中标编号 - 使用publicityNumber字段
                tender_reference = item_json.get('publicityNumber', '')
                if not tender_reference:
                    tender_reference = item_json.get('tenderReference', '')

            else:
                # 招标数据：使用tenderPrjName字段
                title = item_json.get('tenderPrjName', '').strip()
                if not title:
                    title = item_json.get('prjName', '').strip()

                # 招标价格 - 使用totalInvestment字段 (万元)
                total_investment = item_json.get('totalInvestment')
                if total_investment and isinstance(total_investment, (int, float)) and total_investment > 0:
                    price_text = f"{total_investment}万元"
                    price_normalized = float(total_investment) * 10000  # 转换为元
                else:
                    price_text = ""
                    price_normalized = 0.0

                # 招标日期 - 使用noticeStartDate字段
                publish_date = item_json.get('noticeStartDate', '')

                # 招标编号 - 使用registrationId字段
                tender_reference = item_json.get('registrationId', '')

            # 通用字段处理
            if not title:
                return None  # 跳过没有标题的数据

            # 处理发布日期
            if publish_date and isinstance(publish_date, str):
                # 截取日期部分 "2024-07-11 19:00" -> "2024-07-11"
                if len(publish_date) >= 10:
                    publish_date = publish_date[:10]
            else:
                publish_date = "2025-01-01"

            # 构建详情页URL
            item_id = item_json.get('id', '')
            if item_id:
                if announcement_type == '中标':
                    url = f"{self.base_url}/V2PRTS/WinBidBulletinInfoDetail.do?id={item_id}"
                else:
                    url = f"{self.base_url}/V2PRTS/TendererNoticeInfoDetail.do?id={item_id}"
            else:
                url = self.api_endpoints.get(announcement_type, '')

            # 地区信息（武汉地区统一）
            region = "武汉市"

            # 创建BiddingData对象
            item = BiddingData(
                platform_id=self.platform_id,
                title=title,
                url=url,
                publish_date=publish_date,
                region=region,
                announcement_type=announcement_type,
                price_text=price_text,
                price_normalized=price_normalized,
                price_type="WINNING" if announcement_type == '中标' and price_text else ("INVESTMENT" if price_text else "UNKNOWN"),
                tender_reference=tender_reference
            )
            return item

        except Exception as e:
            self._log_message(f"解析数据项失败: {e}")
            return None

    # --- 兼容性方法 ---
    async def get_total_items(self, announcement_type: str = '中标') -> int:
        """获取总数据量"""
        basic_data = await self._fetch_data_batch(1, 1, announcement_type)
        return int(basic_data['total']) if basic_data and 'total' in basic_data else 0

    async def get_max_pages(self, announcement_type: str = '中标') -> int:
        """获取最大页数（兼容性方法）"""
        total_items = await self.get_total_items(announcement_type)
        return max(1, (total_items + 999) // 1000)  # 每页1000条

    async def scrape_all_types(self, db: DatabaseManager) -> list:
        """爬取所有类型的数据（中标+招标），按子类进度更新"""
        all_items = []
        announcement_types = ['中标', '招标']
        total_types = len(announcement_types)

        for i, announcement_type in enumerate(announcement_types):
            self._log_message(f"开始爬取{announcement_type}数据...")

            # 爬取当前类型的数据
            type_items = []
            async for item in self.scrape_list_async(db, announcement_type):
                type_items.append(item)

            all_items.extend(type_items)

            # 按子类进度更新：每完成一个类型，进度增加 1/total_types
            progress = ((i + 1) / total_types) * 100
            self._report_progress(f"{announcement_type}数据爬取完成 ({len(type_items)}条)", progress)

        return all_items





    def extract_details(self, item: BiddingData) -> BiddingData:
        """
        提取详情信息（武汉爬虫的详情已在列表页获取）
        """
        # 武汉爬虫的所有信息都在列表API中获取，无需额外提取详情
        return item

    # --- 基类抽象方法实现（兼容性） ---
    def scrape_list(self, *args, **kwargs):
        """
        方法重载：根据参数类型决定调用同步还是异步版本
        - 如果第一个参数是DatabaseManager，调用异步版本
        - 如果第一个参数是int，这是基类的同步版本
        """
        if len(args) > 0:
            # 检查第一个参数类型
            if hasattr(args[0], 'count_items'):  # DatabaseManager的特征方法
                # 这是GUI调用的异步版本
                return self._scrape_list_async_wrapper(*args, **kwargs)
            elif isinstance(args[0], int):
                # 这是基类的同步版本
                return []  # 简化实现

        # 默认返回空列表
        return []

    async def _scrape_list_async_wrapper(self, db, announcement_type='中标'):
        """异步方法包装器"""
        async for item in self.scrape_list_async(db, announcement_type):
            yield item
