#!/usr/bin/env python3
"""
测试关联优化效果
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from utils.association_optimizer import AssociationOptimizer
from utils.project_matcher import ProjectMatcher
from core.db_manager import DatabaseManager
from models.data_model import BiddingData

def test_association_optimization():
    """测试关联优化效果"""
    print("🔍 测试关联逻辑优化效果...")
    print("=" * 60)
    
    # 1. 分析数据库关联潜力
    print("\n📊 数据库关联潜力分析:")
    analysis = AssociationOptimizer.suggest_association_improvements()
    
    if 'error' in analysis:
        print(f"❌ 分析失败: {analysis['error']}")
        return
    
    # 显示数据库统计
    db_stats = analysis.get('database_stats', {})
    print(f"总记录数: {db_stats.get('total_records', 0):,}")
    print(f"样本大小: {db_stats.get('sample_size', 0):,}")
    
    type_dist = db_stats.get('type_distribution', {})
    for ann_type, count in type_dist.items():
        print(f"  {ann_type}: {count:,} 条")
    
    # 显示编号字段覆盖率
    print(f"\n📋 编号字段覆盖率:")
    ref_coverage = analysis.get('reference_coverage', {})
    for field, info in ref_coverage.items():
        field_name = {
            'tender_reference': '招标编号',
            'publicity_number': '公示编号',
            'registration_id': '登记编号',
            'section_number': '标段编号'
        }.get(field, field)
        
        print(f"  {field_name}: {info['count']:,} 条 ({info['percentage']:.1f}%)")
    
    # 显示标题模式分析
    print(f"\n🏷️ 标题模式分析:")
    title_patterns = analysis.get('title_patterns', {})
    
    common_keywords = title_patterns.get('common_keywords', {})
    if common_keywords:
        print("  常见关键词:")
        for keyword, count in sorted(common_keywords.items(), key=lambda x: x[1], reverse=True):
            print(f"    {keyword}: {count} 次")
    
    announcement_indicators = title_patterns.get('announcement_indicators', {})
    if announcement_indicators:
        print("  公告类型指示词:")
        for indicator, count in sorted(announcement_indicators.items(), key=lambda x: x[1], reverse=True):
            print(f"    {indicator}: {count} 次")
    
    # 显示优化建议
    print(f"\n💡 优化建议:")
    suggestions = analysis.get('suggestions', [])
    for suggestion in suggestions:
        print(f"  {suggestion}")
    
    # 2. 测试增强的匹配算法
    print(f"\n" + "=" * 60)
    print("🧪 测试增强的匹配算法:")
    
    # 获取测试数据
    db = DatabaseManager()
    try:
        # 获取一些有编号的数据进行测试
        test_sql = """
        SELECT * FROM bidding_data 
        WHERE (tender_reference IS NOT NULL AND tender_reference != '') 
           OR (section_number IS NOT NULL AND section_number != '')
        ORDER BY scraped_timestamp DESC 
        LIMIT 50
        """
        
        db.cursor.execute(test_sql)
        test_rows = db.cursor.fetchall()
        
        if test_rows:
            # 转换为BiddingData对象
            column_names = [description[0] for description in db.cursor.description]
            test_data = []
            
            for row in test_rows:
                row_dict = dict(zip(column_names, row))
                row_dict['db_id'] = row_dict.get('db_id')
                test_data.append(BiddingData(**row_dict))
            
            print(f"使用 {len(test_data)} 条测试数据...")
            
            # 使用增强的匹配算法
            matches = ProjectMatcher.find_project_matches(test_data)
            
            # 统计匹配结果
            match_stats = {
                'none': 0,
                'reference_match': 0,
                'high_unique': 0,
                'low_or_multiple': 0
            }
            
            detailed_results = []
            
            for item_key, match_info in matches.items():
                match_status = match_info['match_status']
                match_details = match_info.get('match_details', '')
                current_item = match_info['current_item']
                tender_matches = match_info.get('tender_matches', [])
                
                match_stats[match_status] = match_stats.get(match_status, 0) + 1
                
                # 收集详细结果用于展示
                if match_status != 'none' and len(detailed_results) < 5:
                    detailed_results.append({
                        'title': current_item.title[:50] + "...",
                        'type': current_item.announcement_type,
                        'status': match_status,
                        'details': match_details,
                        'matches_count': len(tender_matches)
                    })
            
            # 显示统计结果
            total_matches = len(matches)
            print(f"\n匹配结果统计 (基于{total_matches}个项目):")
            
            status_names = {
                'none': '无匹配',
                'reference_match': '编号精确匹配',
                'high_unique': '高质量唯一匹配',
                'low_or_multiple': '低质量或多重匹配'
            }
            
            for status, count in match_stats.items():
                if count > 0:
                    rate = (count / total_matches * 100) if total_matches > 0 else 0
                    print(f"  {status_names.get(status, status)}: {count} 项 ({rate:.1f}%)")
            
            # 显示详细示例
            if detailed_results:
                print(f"\n📋 匹配示例:")
                for i, result in enumerate(detailed_results, 1):
                    print(f"\n  示例 {i}:")
                    print(f"    项目: {result['title']}")
                    print(f"    类型: {result['type']}")
                    print(f"    匹配状态: {status_names.get(result['status'], result['status'])}")
                    print(f"    匹配详情: {result['details']}")
                    print(f"    找到匹配: {result['matches_count']} 个")
        
        else:
            print("❌ 没有找到适合的测试数据")
    
    except Exception as e:
        print(f"❌ 测试过程中发生错误: {e}")
        import traceback
        traceback.print_exc()
    
    finally:
        db.close()
    
    # 3. 测试编号提取优化
    print(f"\n" + "=" * 60)
    print("🔧 测试编号提取优化:")
    
    test_texts = [
        "项目编号：HNZB2024-001 招标公告",
        "招标编号: XM-2024-0156 某某工程施工招标",
        "公示编号：GS2024-089 中标结果公示",
        "标段：第一标段 登记编号：DJ-2024-012",
        "采购编号 CG2024-078 设备采购项目"
    ]
    
    for text in test_texts:
        print(f"\n测试文本: {text}")
        extracted = AssociationOptimizer.optimize_reference_extraction(text)
        if extracted:
            for field, value in extracted.items():
                field_name = {
                    'tender_reference': '招标编号',
                    'publicity_number': '公示编号',
                    'registration_id': '登记编号',
                    'section_number': '标段编号'
                }.get(field, field)
                print(f"  提取到 {field_name}: {value}")
        else:
            print("  未提取到编号信息")
    
    print(f"\n" + "=" * 60)
    print("✅ 关联优化测试完成！")
    
    # 4. 生成优化报告
    print(f"\n📄 优化效果总结:")
    print("1. ✅ 增强了匹配算法，支持编号精确匹配和标题相似度匹配")
    print("2. ✅ 改进了GUI显示，提供详细的匹配信息和状态")
    print("3. ✅ 添加了智能关联分析和优化建议")
    print("4. ✅ 优化了编号提取算法，提高字段填充率")
    print("5. ✅ 实现了双向匹配，支持招标->中标和中标->招标")
    print("6. ✅ 增加了增强的匹配对话框，提供更好的用户体验")

if __name__ == "__main__":
    test_association_optimization()
