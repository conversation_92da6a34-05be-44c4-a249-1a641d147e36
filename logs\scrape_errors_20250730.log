
================================================================================
时间: 2025-07-30 11:30:35
平台: 武汉市公共资源交易平台
错误页数: [0]
错误类型: RuntimeError
错误原因: cannot schedule new futures after shutdown
详细堆栈:
Traceback (most recent call last):
  File "w:\工作\2025.7\招投标数据库2.0\gui\task_manager.py", line 370, in _execute_scrape_with_progress
    async for item in scraper.scrape_list(db, announcement_type):
        page_items.append(item)
  File "w:\工作\2025.7\招投标数据库2.0\scrapers\whzbtb_hybrid_scraper.py", line 221, in scrape_list
    batch_data = await self._fetch_data_batch(1, current_batch_size, announcement_type)
                 ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "w:\工作\2025.7\招投标数据库2.0\scrapers\whzbtb_hybrid_scraper.py", line 115, in _fetch_data_batch
    return await asyncio.to_thread(self._fetch_data_with_requests, page_number, rows_per_page, announcement_type)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\asyncio\threads.py", line 25, in to_thread
    return await loop.run_in_executor(None, func_call)
                 ~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\asyncio\base_events.py", line 901, in run_in_executor
    executor.submit(func, *args), loop=self)
    ~~~~~~~~~~~~~~~^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\concurrent\futures\thread.py", line 171, in submit
    raise RuntimeError('cannot schedule new futures after shutdown')
RuntimeError: cannot schedule new futures after shutdown

================================================================================


================================================================================
时间: 2025-07-30 11:30:35
平台: 武汉市公共资源交易平台
错误页数: [0]
错误类型: TypeError
错误原因: '<' not supported between instances of 'list' and 'int'
详细堆栈:
Traceback (most recent call last):
  File "w:\工作\2025.7\招投标数据库2.0\gui\task_manager.py", line 370, in _execute_scrape_with_progress
    async for item in scraper.scrape_list(db, announcement_type):
        page_items.append(item)
  File "w:\工作\2025.7\招投标数据库2.0\scrapers\whzbtb_hybrid_scraper.py", line 221, in scrape_list
    batch_data = await self._fetch_data_batch(1, current_batch_size, announcement_type)
                 ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "w:\工作\2025.7\招投标数据库2.0\scrapers\whzbtb_hybrid_scraper.py", line 115, in _fetch_data_batch
    return await asyncio.to_thread(self._fetch_data_with_requests, page_number, rows_per_page, announcement_type)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\asyncio\threads.py", line 25, in to_thread
    return await loop.run_in_executor(None, func_call)
                 ~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\asyncio\base_events.py", line 901, in run_in_executor
    executor.submit(func, *args), loop=self)
    ~~~~~~~~~~~~~~~^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\concurrent\futures\thread.py", line 171, in submit
    raise RuntimeError('cannot schedule new futures after shutdown')
RuntimeError: cannot schedule new futures after shutdown

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "w:\工作\2025.7\招投标数据库2.0\gui\task_manager.py", line 502, in _execute_scrape_with_progress
    if current_page < max_pages - 3:
       ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
TypeError: '<' not supported between instances of 'list' and 'int'

================================================================================


================================================================================
时间: 2025-07-30 15:07:41
平台: 武汉市公共资源交易平台
错误页数: [0]
错误类型: TypeError
错误原因: '>' not supported between instances of 'list' and 'int'
详细堆栈:
Traceback (most recent call last):
  File "w:\工作\2025.7\招投标数据库2.0\gui\task_manager.py", line 481, in _execute_scrape_with_progress
    self.send_progress_with_eta(platform_id, current_page, max_pages, "scrape", progress_data)
    ~~~~~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "w:\工作\2025.7\招投标数据库2.0\gui\task_manager.py", line 816, in send_progress_with_eta
    if platform_id in self.task_start_times and current > 0:
                                                ^^^^^^^^^^^
TypeError: '>' not supported between instances of 'list' and 'int'

================================================================================


================================================================================
时间: 2025-07-30 15:07:41
平台: 武汉市公共资源交易平台
错误页数: [0]
错误类型: TypeError
错误原因: '<' not supported between instances of 'list' and 'int'
详细堆栈:
Traceback (most recent call last):
  File "w:\工作\2025.7\招投标数据库2.0\gui\task_manager.py", line 481, in _execute_scrape_with_progress
    self.send_progress_with_eta(platform_id, current_page, max_pages, "scrape", progress_data)
    ~~~~~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "w:\工作\2025.7\招投标数据库2.0\gui\task_manager.py", line 816, in send_progress_with_eta
    if platform_id in self.task_start_times and current > 0:
                                                ^^^^^^^^^^^
TypeError: '>' not supported between instances of 'list' and 'int'

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "w:\工作\2025.7\招投标数据库2.0\gui\task_manager.py", line 520, in _execute_scrape_with_progress
    if current_page < max_pages - 3:
       ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
TypeError: '<' not supported between instances of 'list' and 'int'

================================================================================


================================================================================
时间: 2025-07-30 15:13:45
平台: 武汉市公共资源交易平台
错误页数: [28]
错误类型: TypeError
错误原因: '>' not supported between instances of 'list' and 'int'
详细堆栈:
Traceback (most recent call last):
  File "w:\工作\2025.7\招投标数据库2.0\gui\task_manager.py", line 481, in _execute_scrape_with_progress
    self.send_progress_with_eta(platform_id, current_page, max_pages, "scrape", progress_data)
    ~~~~~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "w:\工作\2025.7\招投标数据库2.0\gui\task_manager.py", line 816, in send_progress_with_eta
    if platform_id in self.task_start_times and current > 0:
                                                ^^^^^^^^^^^
TypeError: '>' not supported between instances of 'list' and 'int'

================================================================================


================================================================================
时间: 2025-07-30 15:13:45
平台: 武汉市公共资源交易平台
错误页数: [28]
错误类型: TypeError
错误原因: '<' not supported between instances of 'list' and 'int'
详细堆栈:
Traceback (most recent call last):
  File "w:\工作\2025.7\招投标数据库2.0\gui\task_manager.py", line 481, in _execute_scrape_with_progress
    self.send_progress_with_eta(platform_id, current_page, max_pages, "scrape", progress_data)
    ~~~~~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "w:\工作\2025.7\招投标数据库2.0\gui\task_manager.py", line 816, in send_progress_with_eta
    if platform_id in self.task_start_times and current > 0:
                                                ^^^^^^^^^^^
TypeError: '>' not supported between instances of 'list' and 'int'

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "w:\工作\2025.7\招投标数据库2.0\gui\task_manager.py", line 520, in _execute_scrape_with_progress
    if current_page < max_pages - 3:
       ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
TypeError: '<' not supported between instances of 'list' and 'int'

================================================================================


================================================================================
时间: 2025-07-30 15:47:55
平台: 武汉市公共资源交易平台
错误页数: 1
错误类型: TypeError
错误原因: 'async for' requires an object with __aiter__ method, got list
详细堆栈:
Traceback (most recent call last):
  File "w:\工作\2025.7\招投标数据库2.0\gui\task_manager.py", line 374, in _execute_scrape_with_progress
    async for item in scraper.scrape_list(db, announcement_type):
                      ~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^
TypeError: 'async for' requires an object with __aiter__ method, got list

================================================================================


================================================================================
时间: 2025-07-30 15:47:56
平台: 武汉市公共资源交易平台
错误页数: 1
错误类型: TypeError
错误原因: 'async for' requires an object with __aiter__ method, got list
详细堆栈:
Traceback (most recent call last):
  File "w:\工作\2025.7\招投标数据库2.0\gui\task_manager.py", line 374, in _execute_scrape_with_progress
    async for item in scraper.scrape_list(db, announcement_type):
                      ~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^
TypeError: 'async for' requires an object with __aiter__ method, got list

================================================================================


================================================================================
时间: 2025-07-30 15:48:00
平台: 武汉市公共资源交易平台
错误页数: 2
错误类型: TypeError
错误原因: 'async for' requires an object with __aiter__ method, got list
详细堆栈:
Traceback (most recent call last):
  File "w:\工作\2025.7\招投标数据库2.0\gui\task_manager.py", line 374, in _execute_scrape_with_progress
    async for item in scraper.scrape_list(db, announcement_type):
                      ~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^
TypeError: 'async for' requires an object with __aiter__ method, got list

================================================================================


================================================================================
时间: 2025-07-30 15:48:02
平台: 武汉市公共资源交易平台
错误页数: 2
错误类型: TypeError
错误原因: 'async for' requires an object with __aiter__ method, got list
详细堆栈:
Traceback (most recent call last):
  File "w:\工作\2025.7\招投标数据库2.0\gui\task_manager.py", line 374, in _execute_scrape_with_progress
    async for item in scraper.scrape_list(db, announcement_type):
                      ~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^
TypeError: 'async for' requires an object with __aiter__ method, got list

================================================================================


================================================================================
时间: 2025-07-30 15:48:06
平台: 武汉市公共资源交易平台
错误页数: 3
错误类型: TypeError
错误原因: 'async for' requires an object with __aiter__ method, got list
详细堆栈:
Traceback (most recent call last):
  File "w:\工作\2025.7\招投标数据库2.0\gui\task_manager.py", line 374, in _execute_scrape_with_progress
    async for item in scraper.scrape_list(db, announcement_type):
                      ~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^
TypeError: 'async for' requires an object with __aiter__ method, got list

================================================================================


================================================================================
时间: 2025-07-30 15:48:08
平台: 武汉市公共资源交易平台
错误页数: 3
错误类型: TypeError
错误原因: 'async for' requires an object with __aiter__ method, got list
详细堆栈:
Traceback (most recent call last):
  File "w:\工作\2025.7\招投标数据库2.0\gui\task_manager.py", line 374, in _execute_scrape_with_progress
    async for item in scraper.scrape_list(db, announcement_type):
                      ~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^
TypeError: 'async for' requires an object with __aiter__ method, got list

================================================================================


================================================================================
时间: 2025-07-30 15:48:11
平台: 武汉市公共资源交易平台
错误页数: 4
错误类型: TypeError
错误原因: 'async for' requires an object with __aiter__ method, got list
详细堆栈:
Traceback (most recent call last):
  File "w:\工作\2025.7\招投标数据库2.0\gui\task_manager.py", line 374, in _execute_scrape_with_progress
    async for item in scraper.scrape_list(db, announcement_type):
                      ~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^
TypeError: 'async for' requires an object with __aiter__ method, got list

================================================================================


================================================================================
时间: 2025-07-30 15:48:14
平台: 武汉市公共资源交易平台
错误页数: 4
错误类型: TypeError
错误原因: 'async for' requires an object with __aiter__ method, got list
详细堆栈:
Traceback (most recent call last):
  File "w:\工作\2025.7\招投标数据库2.0\gui\task_manager.py", line 374, in _execute_scrape_with_progress
    async for item in scraper.scrape_list(db, announcement_type):
                      ~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^
TypeError: 'async for' requires an object with __aiter__ method, got list

================================================================================


================================================================================
时间: 2025-07-30 15:48:17
平台: 武汉市公共资源交易平台
错误页数: 5
错误类型: TypeError
错误原因: 'async for' requires an object with __aiter__ method, got list
详细堆栈:
Traceback (most recent call last):
  File "w:\工作\2025.7\招投标数据库2.0\gui\task_manager.py", line 374, in _execute_scrape_with_progress
    async for item in scraper.scrape_list(db, announcement_type):
                      ~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^
TypeError: 'async for' requires an object with __aiter__ method, got list

================================================================================


================================================================================
时间: 2025-07-30 15:48:20
平台: 武汉市公共资源交易平台
错误页数: 5
错误类型: TypeError
错误原因: 'async for' requires an object with __aiter__ method, got list
详细堆栈:
Traceback (most recent call last):
  File "w:\工作\2025.7\招投标数据库2.0\gui\task_manager.py", line 374, in _execute_scrape_with_progress
    async for item in scraper.scrape_list(db, announcement_type):
                      ~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^
TypeError: 'async for' requires an object with __aiter__ method, got list

================================================================================

