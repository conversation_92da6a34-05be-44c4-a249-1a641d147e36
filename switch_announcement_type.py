#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
公告类型快速切换工具
用于快速切换爬取的公告类型（中标/招标）
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from core.scheduler_config import SchedulerConfig

def main():
    """主函数"""
    config = SchedulerConfig()
    
    print("=" * 50)
    print("🔧 公告类型切换工具")
    print("=" * 50)
    
    # 显示当前设置
    current_type = config.get_announcement_type()
    print(f"📋 当前公告类型: {current_type}")
    
    print("\n可选择的公告类型:")
    print("1. 中标 - 爬取中标公告")
    print("2. 招标 - 爬取招标公告")
    print("0. 退出")
    
    while True:
        try:
            choice = input(f"\n请选择要切换的类型 (当前: {current_type}): ").strip()
            
            if choice == "0":
                print("👋 退出程序")
                break
            elif choice == "1":
                config.set_announcement_type("中标")
                print("✅ 已切换到: 中标公告")
                print("💡 现在运行爬虫将爬取中标数据")
                break
            elif choice == "2":
                config.set_announcement_type("招标")
                print("✅ 已切换到: 招标公告")
                print("💡 现在运行爬虫将爬取招标数据")
                break
            else:
                print("❌ 无效选择，请输入 0、1 或 2")
                
        except KeyboardInterrupt:
            print("\n👋 用户取消操作")
            break
        except Exception as e:
            print(f"❌ 操作失败: {e}")
            break
    
    print("\n" + "=" * 50)

if __name__ == "__main__":
    main()
