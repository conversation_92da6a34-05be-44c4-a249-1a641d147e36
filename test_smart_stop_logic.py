#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
测试智能停止逻辑的脚本
验证连续重复数据检测和停止机制
"""

import asyncio
import sys
import os

# 添加项目根目录到路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from database.database_manager import DatabaseManager
from models.data_model import BiddingData
from datetime import date, datetime

class SmartStopTester:
    """智能停止逻辑测试器"""
    
    def __init__(self):
        self.db = DatabaseManager()
        self.consecutive_duplicate_pages = 0
        self.max_consecutive_duplicates = 3
        
    def simulate_page_data(self, page_num: int, has_new_data: bool = True):
        """模拟页面数据"""
        if has_new_data:
            # 创建新数据
            return [
                BiddingData(
                    platform_id="test_platform",
                    title=f"测试项目 {page_num}-{i}",
                    url=f"http://test.com/page{page_num}/item{i}",
                    publish_date=date.today(),
                    announcement_type="中标",
                    price_type="UNKNOWN"
                ) for i in range(5)  # 每页5条数据
            ]
        else:
            # 返回重复数据（已存在于数据库中）
            return [
                BiddingData(
                    platform_id="test_platform", 
                    title="重复项目",
                    url="http://test.com/duplicate",
                    publish_date=date.today(),
                    announcement_type="中标",
                    price_type="UNKNOWN"
                )
            ]
    
    def test_smart_stop_logic(self):
        """测试智能停止逻辑"""
        print("🧪 开始测试智能停止逻辑...")
        print(f"📋 配置: 连续{self.max_consecutive_duplicates}页重复数据时停止")
        print("="*60)
        
        # 先插入一些重复数据到数据库
        duplicate_item = BiddingData(
            platform_id="test_platform",
            title="重复项目",
            url="http://test.com/duplicate", 
            publish_date=date.today(),
            announcement_type="中标",
            price_type="UNKNOWN"
        )
        self.db.save_item(duplicate_item)
        print("✅ 已插入重复数据到数据库")
        
        # 模拟爬取过程
        page_scenarios = [
            (1, True),   # 第1页：有新数据
            (2, True),   # 第2页：有新数据  
            (3, False),  # 第3页：重复数据
            (4, False),  # 第4页：重复数据
            (5, False),  # 第5页：重复数据 - 应该在这里停止
            (6, True),   # 第6页：有新数据 - 不应该到达这里
            (7, True),   # 第7页：有新数据 - 不应该到达这里
        ]
        
        should_stop = False
        total_new_items = 0
        
        for page_num, has_new_data in page_scenarios:
            if should_stop:
                print(f"🛑 第{page_num}页：智能停止生效，跳过后续页面")
                break
                
            print(f"\n📄 正在处理第{page_num}页...")
            
            # 模拟获取页面数据
            page_items = self.simulate_page_data(page_num, has_new_data)
            
            # 处理数据并统计新增
            page_new_items = 0
            for item in page_items:
                if self.db.save_item(item):
                    page_new_items += 1
                    total_new_items += 1
            
            print(f"   📊 新增数据: {page_new_items} 条")
            
            # 智能停止逻辑检测
            if page_new_items == 0:
                self.consecutive_duplicate_pages += 1
                print(f"   ⚠️  检测到重复页面 ({self.consecutive_duplicate_pages}/{self.max_consecutive_duplicates})")
                
                if self.consecutive_duplicate_pages >= self.max_consecutive_duplicates:
                    print(f"   🛑 连续{self.max_consecutive_duplicates}页均为重复数据，触发智能停止！")
                    should_stop = True
            else:
                # 有新数据时重置计数器
                if self.consecutive_duplicate_pages > 0:
                    print(f"   🔄 发现新数据，重置重复页面计数器")
                self.consecutive_duplicate_pages = 0
        
        print("\n" + "="*60)
        print("📈 测试结果统计:")
        print(f"   📊 总新增数据: {total_new_items} 条")
        print(f"   🔢 连续重复页面: {self.consecutive_duplicate_pages} 页")
        print(f"   🛑 是否触发智能停止: {'是' if should_stop else '否'}")
        
        # 验证结果
        if should_stop and self.consecutive_duplicate_pages >= self.max_consecutive_duplicates:
            print("✅ 智能停止逻辑测试通过！")
            return True
        else:
            print("❌ 智能停止逻辑测试失败！")
            return False
    
    def test_reset_logic(self):
        """测试重置逻辑"""
        print("\n🔄 测试重置逻辑...")
        print("="*60)
        
        # 模拟场景：重复->重复->新数据->重复->重复->重复
        scenarios = [
            (1, False, "重复数据1"),
            (2, False, "重复数据2"), 
            (3, True,  "新数据，应重置计数器"),
            (4, False, "重复数据3"),
            (5, False, "重复数据4"),
            (6, False, "重复数据5，应触发停止"),
        ]
        
        self.consecutive_duplicate_pages = 0
        should_stop = False
        
        for page_num, has_new_data, description in scenarios:
            if should_stop:
                break
                
            print(f"\n📄 第{page_num}页: {description}")
            
            # 模拟数据处理
            if has_new_data:
                page_new_items = 3  # 模拟新增3条数据
            else:
                page_new_items = 0  # 模拟无新增数据
            
            print(f"   📊 新增数据: {page_new_items} 条")
            
            # 智能停止逻辑
            if page_new_items == 0:
                self.consecutive_duplicate_pages += 1
                print(f"   ⚠️  重复页面计数: {self.consecutive_duplicate_pages}/{self.max_consecutive_duplicates}")
                
                if self.consecutive_duplicate_pages >= self.max_consecutive_duplicates:
                    print(f"   🛑 触发智能停止！")
                    should_stop = True
            else:
                if self.consecutive_duplicate_pages > 0:
                    print(f"   🔄 重置计数器 (从{self.consecutive_duplicate_pages}重置为0)")
                self.consecutive_duplicate_pages = 0
        
        print("\n📈 重置逻辑测试结果:")
        print(f"   🔢 最终重复页面计数: {self.consecutive_duplicate_pages}")
        print(f"   🛑 是否正确触发停止: {'是' if should_stop else '否'}")
        
        if should_stop:
            print("✅ 重置逻辑测试通过！")
            return True
        else:
            print("❌ 重置逻辑测试失败！")
            return False
    
    def cleanup(self):
        """清理测试数据"""
        print("\n🧹 清理测试数据...")
        try:
            # 删除测试数据
            cursor = self.db.connection.cursor()
            cursor.execute("DELETE FROM bidding_data WHERE platform_id = 'test_platform'")
            self.db.connection.commit()
            print("✅ 测试数据清理完成")
        except Exception as e:
            print(f"❌ 清理测试数据失败: {e}")
        finally:
            self.db.close()

def main():
    """主测试函数"""
    print("🚀 智能停止逻辑测试开始")
    print("="*60)
    
    tester = SmartStopTester()
    
    try:
        # 测试1: 基本智能停止逻辑
        test1_passed = tester.test_smart_stop_logic()
        
        # 测试2: 重置逻辑
        test2_passed = tester.test_reset_logic()
        
        # 总结
        print("\n" + "="*60)
        print("🎯 测试总结:")
        print(f"   📋 基本智能停止逻辑: {'✅ 通过' if test1_passed else '❌ 失败'}")
        print(f"   🔄 重置逻辑测试: {'✅ 通过' if test2_passed else '❌ 失败'}")
        
        if test1_passed and test2_passed:
            print("\n🎉 所有测试通过！智能停止逻辑工作正常。")
        else:
            print("\n⚠️  部分测试失败，需要检查智能停止逻辑实现。")
            
    except Exception as e:
        print(f"❌ 测试过程中发生错误: {e}")
        import traceback
        traceback.print_exc()
    finally:
        tester.cleanup()

if __name__ == "__main__":
    main()
