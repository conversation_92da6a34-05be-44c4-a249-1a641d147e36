#!/usr/bin/env python3
"""
调试倒序页码计算问题
检查数据库中存储的倒序页码数据是否合理
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from core.db_manager import DatabaseManager
from scrapers.cnzhaobiao_scraper import CnzhaobiaoScraper

def debug_reverse_pages():
    """调试倒序页码问题"""
    print("🔍 开始调试倒序页码计算问题...")
    
    db = DatabaseManager()
    scraper = CnzhaobiaoScraper()
    
    try:
        # 1. 获取当前总页数
        print("\n📊 获取当前总页数...")
        import asyncio
        
        async def get_pages():
            zhongbiao_pages = await scraper.get_max_pages('中标')
            zhaobiao_pages = await scraper.get_max_pages('招标')
            return zhongbiao_pages, zhaobiao_pages
        
        zhongbiao_total, zhaobiao_total = asyncio.run(get_pages())
        print(f"中标公告总页数: {zhongbiao_total}")
        print(f"招标公告总页数: {zhaobiao_total}")
        
        # 2. 检查数据库中的倒序页码数据
        print("\n🗄️ 检查数据库中的倒序页码数据...")
        
        # 检查中标数据
        zhongbiao_scraped = db.get_scraped_reverse_pages('cnzhaobiao', '中标')
        print(f"中标公告已爬取页面数: {len(zhongbiao_scraped)}")
        if zhongbiao_scraped:
            min_reverse = min(zhongbiao_scraped)
            max_reverse = max(zhongbiao_scraped)
            print(f"中标公告倒序页码范围: {min_reverse} - {max_reverse}")
            
            # 检查是否有异常的倒序页码
            invalid_pages = [p for p in zhongbiao_scraped if p > zhongbiao_total or p < 1]
            if invalid_pages:
                print(f"⚠️ 发现异常的中标倒序页码: {sorted(invalid_pages)[:10]}...")
                print(f"异常页码数量: {len(invalid_pages)}")
            
            # 计算对应的正序页码
            print(f"倒序页码 {min_reverse} 对应正序页码: {zhongbiao_total - min_reverse + 1}")
            print(f"倒序页码 {max_reverse} 对应正序页码: {zhongbiao_total - max_reverse + 1}")
        
        # 检查招标数据
        zhaobiao_scraped = db.get_scraped_reverse_pages('cnzhaobiao', '招标')
        print(f"\n招标公告已爬取页面数: {len(zhaobiao_scraped)}")
        if zhaobiao_scraped:
            min_reverse = min(zhaobiao_scraped)
            max_reverse = max(zhaobiao_scraped)
            print(f"招标公告倒序页码范围: {min_reverse} - {max_reverse}")
            
            # 检查是否有异常的倒序页码
            invalid_pages = [p for p in zhaobiao_scraped if p > zhaobiao_total or p < 1]
            if invalid_pages:
                print(f"⚠️ 发现异常的招标倒序页码: {sorted(invalid_pages)[:10]}...")
                print(f"异常页码数量: {len(invalid_pages)}")
            
            # 计算对应的正序页码
            print(f"倒序页码 {min_reverse} 对应正序页码: {zhaobiao_total - min_reverse + 1}")
            print(f"倒序页码 {max_reverse} 对应正序页码: {zhaobiao_total - max_reverse + 1}")
        
        # 3. 查询一些具体的数据记录
        print("\n📋 查询具体的数据记录...")
        query_sql = """
        SELECT title, reverse_page_number, announcement_type, publish_date
        FROM bidding_data 
        WHERE platform_id = 'cnzhaobiao' AND reverse_page_number IS NOT NULL
        ORDER BY reverse_page_number DESC
        LIMIT 10
        """
        
        db.cursor.execute(query_sql)
        records = db.cursor.fetchall()
        
        print("最大倒序页码的记录:")
        for record in records:
            title, reverse_page, ann_type, pub_date = record
            total_for_type = zhongbiao_total if ann_type == '中标' else zhaobiao_total
            normal_page = total_for_type - reverse_page + 1
            print(f"  倒序页码: {reverse_page}, 正序页码: {normal_page}, 类型: {ann_type}, 日期: {pub_date}")
            print(f"    标题: {title[:50]}...")
        
        # 4. 统计异常数据
        print("\n📈 统计异常数据...")
        
        # 统计中标异常数据
        query_sql = """
        SELECT COUNT(*) FROM bidding_data 
        WHERE platform_id = 'cnzhaobiao' AND announcement_type = '中标' 
        AND reverse_page_number > ?
        """
        db.cursor.execute(query_sql, (zhongbiao_total,))
        zhongbiao_invalid_count = db.cursor.fetchone()[0]
        
        # 统计招标异常数据
        query_sql = """
        SELECT COUNT(*) FROM bidding_data 
        WHERE platform_id = 'cnzhaobiao' AND announcement_type = '招标' 
        AND reverse_page_number > ?
        """
        db.cursor.execute(query_sql, (zhaobiao_total,))
        zhaobiao_invalid_count = db.cursor.fetchone()[0]
        
        print(f"中标公告异常倒序页码记录数: {zhongbiao_invalid_count}")
        print(f"招标公告异常倒序页码记录数: {zhaobiao_invalid_count}")
        
        # 5. 提供修复建议
        print("\n💡 修复建议:")
        if zhongbiao_invalid_count > 0 or zhaobiao_invalid_count > 0:
            print("发现异常的倒序页码数据，可能的原因:")
            print("1. 网站总页数发生了变化（页面被删除或归档）")
            print("2. 之前的爬取使用了错误的总页数")
            print("3. 数据库中混合了不同时期的数据")
            print("\n建议的修复方案:")
            print("1. 清理异常的倒序页码数据")
            print("2. 重新计算并更新倒序页码")
            print("3. 或者清空倒序页码字段，重新开始断点续爬")
        else:
            print("倒序页码数据看起来正常")
    
    except Exception as e:
        print(f"❌ 调试过程中发生错误: {e}")
        import traceback
        traceback.print_exc()
    
    finally:
        db.close()

if __name__ == "__main__":
    debug_reverse_pages()
