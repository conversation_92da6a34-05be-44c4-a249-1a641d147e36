#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
清空数据库中的武汉爬虫数据
"""

import sys
import os

# 添加项目根目录到路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from core.db_manager import DatabaseManager

def clear_wuhan_data():
    """清空武汉爬虫的数据"""
    print("🗑️ 清空武汉爬虫数据")
    print("=" * 50)
    
    try:
        # 创建数据库管理器
        db = DatabaseManager()
        
        # 查询武汉爬虫的数据量
        cursor = db.conn.cursor()
        
        # 查询招标数据
        cursor.execute("SELECT COUNT(*) FROM bidding_data WHERE platform_id = 'whzbtb' AND announcement_type = '招标'")
        tender_count = cursor.fetchone()[0]
        
        # 查询中标数据
        cursor.execute("SELECT COUNT(*) FROM bidding_data WHERE platform_id = 'whzbtb' AND announcement_type = '中标'")
        winbid_count = cursor.fetchone()[0]
        
        total_count = tender_count + winbid_count
        
        print(f"📊 当前武汉爬虫数据统计:")
        print(f"  招标数据: {tender_count} 条")
        print(f"  中标数据: {winbid_count} 条")
        print(f"  总计: {total_count} 条")
        
        if total_count == 0:
            print("✅ 数据库中没有武汉爬虫数据，无需清空")
            return
        
        # 确认删除
        print(f"\n⚠️ 即将删除 {total_count} 条武汉爬虫数据")
        
        # 删除武汉爬虫的所有数据
        cursor.execute("DELETE FROM bidding_data WHERE platform_id = 'whzbtb'")
        deleted_count = cursor.rowcount
        
        # 提交更改
        db.conn.commit()
        
        print(f"✅ 成功删除 {deleted_count} 条武汉爬虫数据")
        
        # 验证删除结果
        cursor.execute("SELECT COUNT(*) FROM bidding_data WHERE platform_id = 'whzbtb'")
        remaining_count = cursor.fetchone()[0]
        
        if remaining_count == 0:
            print("✅ 武汉爬虫数据已完全清空")
        else:
            print(f"⚠️ 仍有 {remaining_count} 条数据未删除")
        
        # 查询总数据量
        cursor.execute("SELECT COUNT(*) FROM bidding_data")
        total_remaining = cursor.fetchone()[0]
        print(f"📊 数据库总数据量: {total_remaining} 条")
        
        db.close()
        
    except Exception as e:
        print(f"❌ 清空数据时出错: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    clear_wuhan_data()
